<?php
/**
 * 扫描本地文件处理器
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 检查权限
session_start();

// 如果没有登录，自动设置登录状态（开发环境）
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_username'] = 'admin';
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 设置执行时间限制和内存限制
set_time_limit(60); // 60秒超时
ini_set('memory_limit', '256M');

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['action'])) {
        throw new Exception('无效的请求');
    }
    
    $action = $data['action'];
    $qiniuSync = new QiniuSync();
    $fileManager = $qiniuSync->getFileManager();
    $configManager = $qiniuSync->getConfigManager();

    switch ($action) {
        case 'scan_local_files':
            // 扫描本地文件
            $result = scanLocalFiles($fileManager, $configManager);
            echo json_encode($result);
            break;
            
        default:
            throw new Exception('未知操作: ' . $action);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 扫描本地文件
 */
function scanLocalFiles($fileManager, $configManager)
{
    try {

        // 从配置中读取同步目录列表
        $scanDirs = $configManager->getConfig('sync.directories', [
            QINIU_SYNC_ROOT . '/uploads/images'
            
        ]);

        // 从配置中读取允许的文件扩展名
        $allowedExtensions = $configManager->getConfig('sync.allowed_extensions', [
            'jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'pdf', 'txt', 'doc', 'docx'
        ]);

        $totalFiles = 0;
        $addedFiles = 0;
        $scannedDirs = [];

        foreach ($scanDirs as $dir) {
            if (!is_dir($dir)) {
                $scannedDirs[] = [
                    'path' => $dir,
                    'status' => 'not_found',
                    'total' => 0,
                    'added' => 0
                ];
                continue;
            }

            // 限制每个目录最多扫描500个文件，避免超时
            $result = scanDirectory($dir, $fileManager, $allowedExtensions, 500);
            $totalFiles += $result['total'];
            $addedFiles += $result['added'];

            $scannedDirs[] = [
                'path' => $dir,
                'status' => 'scanned',
                'total' => $result['total'],
                'added' => $result['added'],
                'error' => $result['error'] ?? null
            ];
        }

        return [
            'success' => true,
            'message' => '扫描完成',
            'total' => $totalFiles,
            'added' => $addedFiles,
            'scanned_dirs' => $scannedDirs,
            'config_dirs' => $scanDirs,
            'allowed_extensions' => $allowedExtensions
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '扫描失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 扫描单个目录（优化版本）
 */
function scanDirectory($directory, $fileManager, $allowedExtensions, $maxFiles = 1000)
{
    $totalFiles = 0;
    $addedFiles = 0;
    $startTime = time();
    $maxExecutionTime = 30; // 最大执行时间30秒

    try {
        if (!is_dir($directory)) {
            return ['total' => 0, 'added' => 0, 'error' => '目录不存在'];
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($iterator as $file) {
            // 检查执行时间，避免超时
            if (time() - $startTime > $maxExecutionTime) {
                break;
            }

            // 检查文件数量限制
            if ($totalFiles >= $maxFiles) {
                break;
            }

            if ($file->isFile()) {
                $extension = strtolower($file->getExtension());

                if (in_array($extension, $allowedExtensions)) {
                    $totalFiles++;

                    // 检查文件是否已存在于数据库中
                    $filePath = $file->getPathname();
                    if (!isFileInDatabase($filePath, $fileManager)) {
                        // 添加新文件
                        if ($fileManager->addFile($filePath)) {
                            $addedFiles++;
                        }
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        // 忽略无法访问的目录
    }
    
    return [
        'total' => $totalFiles,
        'added' => $addedFiles
    ];
}

/**
 * 检查文件是否已在数据库中
 */
function isFileInDatabase($filePath, $fileManager)
{
    try {
        $db = $fileManager->getDatabase();
        $stmt = $db->prepare("SELECT id FROM qiniu_sync_files WHERE local_path = ?");
        $stmt->execute([$filePath]);
        
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}
?>
