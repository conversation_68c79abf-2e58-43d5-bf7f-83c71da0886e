<?php
/**
 * 同步功能测试页面
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

echo "<h2>同步功能测试</h2>";

try {
    $qiniuSync = new QiniuSync();
    $syncManager = $qiniuSync->getSyncManager();
    
    echo "<h3>1. 测试启动同步服务</h3>";
    $startResult = $syncManager->start();
    echo "<p>启动结果: " . json_encode($startResult, JSON_UNESCAPED_UNICODE) . "</p>";
    
    echo "<h3>2. 测试手动同步</h3>";
    $manualResult = $syncManager->manualSync(5);
    echo "<p>手动同步结果: " . json_encode($manualResult, JSON_UNESCAPED_UNICODE) . "</p>";
    
    echo "<h3>3. 测试停止同步服务</h3>";
    $stopResult = $syncManager->stop();
    echo "<p>停止结果: " . json_encode($stopResult, JSON_UNESCAPED_UNICODE) . "</p>";
    
    echo "<h3>4. 检查文件管理器</h3>";
    $fileManager = $qiniuSync->getFileManager();
    $pendingFiles = $fileManager->getPendingFiles(5);
    echo "<p>待同步文件数: " . count($pendingFiles) . "</p>";
    
    if (!empty($pendingFiles)) {
        echo "<h4>待同步文件列表:</h4>";
        echo "<ul>";
        foreach ($pendingFiles as $file) {
            echo "<li>" . htmlspecialchars($file['local_path']) . " (状态: " . $file['sync_status'] . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<hr>";
    echo "<h3>测试AJAX接口</h3>";
    echo "<button onclick='testStartSync()'>测试启动同步</button> ";
    echo "<button onclick='testManualSync()'>测试手动同步</button> ";
    echo "<button onclick='testStopSync()'>测试停止同步</button>";
    echo "<div id='result' style='margin-top: 10px; padding: 10px; border: 1px solid #ccc;'></div>";
    
} catch (Exception $e) {
    echo "<p>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<script>
function showResult(message) {
    document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(message, null, 2) + '</pre>';
}

function testStartSync() {
    fetch('ajax.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({action: 'start_sync'})
    })
    .then(response => response.json())
    .then(data => showResult(data))
    .catch(error => showResult({error: error.message}));
}

function testManualSync() {
    fetch('ajax.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({action: 'manual_sync', limit: 3})
    })
    .then(response => response.json())
    .then(data => showResult(data))
    .catch(error => showResult({error: error.message}));
}

function testStopSync() {
    fetch('ajax.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({action: 'stop_sync'})
    })
    .then(response => response.json())
    .then(data => showResult(data))
    .catch(error => showResult({error: error.message}));
}
</script>

<style>
button {
    padding: 8px 16px;
    margin: 4px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
button:hover {
    background: #0056b3;
}
</style>
