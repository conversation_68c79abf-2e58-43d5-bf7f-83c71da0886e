[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:00:07] ERROR: 初始化七牛云失败 {"error":"七牛云配置不完整"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:01:06] ERROR: 初始化七牛云失败 {"error":"七牛云配置不完整"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:11] ERROR: 初始化七牛云失败 {"error":"七牛云配置不完整"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:02:20] ERROR: 初始化七牛云失败 {"error":"七牛云配置不完整"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:10] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"plugin.version","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 配置更新失败 {"key":"test.timestamp","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] INFO: 测试日志记录 {"test":true,"timestamp":1757084604}
[2025-09-05 23:03:24] WARNING: 测试警告日志 
[2025-09-05 23:03:24] ERROR: 测试错误日志 
[2025-09-05 23:03:24] ERROR: 获取文件统计失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_files' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 添加任务到队列失败 {"file_path":"test\/file.jpg","action":"upload","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:24] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取文件统计失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_files' doesn't exist"}
[2025-09-05 23:03:45] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:12] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:04:12] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:04:12] ERROR: 获取文件统计失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_files' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:14] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:04:14] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:04:14] ERROR: 获取队列任务列表失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:04:15] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"plugin.version","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 配置更新失败 {"key":"test.timestamp","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] INFO: 测试日志记录 {"test":true,"timestamp":1757084791}
[2025-09-05 23:06:31] WARNING: 测试警告日志 
[2025-09-05 23:06:31] ERROR: 测试错误日志 
[2025-09-05 23:06:31] ERROR: 获取文件统计失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_files' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 添加任务到队列失败 {"file_path":"test\/file.jpg","action":"upload","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:31] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取文件统计失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_files' doesn't exist"}
[2025-09-05 23:06:46] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取文件统计失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_files' doesn't exist"}
[2025-09-05 23:06:48] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取文件统计失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_files' doesn't exist"}
[2025-09-05 23:06:50] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:07:02] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:35] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:37] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:37] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:10:38] ERROR: 配置更新失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:10:38] ERROR: 批量配置更新失败 {"error":"更新配置失败: qiniu.access_key"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:12:39] ERROR: 配置更新失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:39] ERROR: 批量配置更新失败 {"error":"更新配置失败: qiniu.access_key"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:12:46] ERROR: 配置更新失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:46] ERROR: 批量配置更新失败 {"error":"更新配置失败: qiniu.access_key"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:12:49] ERROR: 配置更新失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:12:49] ERROR: 批量配置更新失败 {"error":"更新配置失败: qiniu.access_key"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:13:33] ERROR: 配置更新失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:33] ERROR: 批量配置更新失败 {"error":"更新配置失败: qiniu.access_key"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:37] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:40] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:41] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:13:41] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:13:41] ERROR: 获取队列任务列表失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:13:42] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:13:42] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:13:42] ERROR: 获取文件统计失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_files' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:14:06] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:06] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:14:42] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:14:42] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:19:07] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:07] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:19:15] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:15] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:19:28] ERROR: 获取配置失败 {"key":"plugin.enabled","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:28] ERROR: 获取队列大小失败 {"error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_queue' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 配置更新失败 {"key":"security.encryption_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"sync.watch_paths","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"sync.allowed_extensions","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"sync.max_file_size","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"qiniu.access_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"qiniu.secret_key","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"qiniu.bucket","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"qiniu.domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"qiniu.prefix","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"qiniu.region","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"qiniu.use_https","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] ERROR: 获取配置失败 {"key":"qiniu.use_cdn_domain","error":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'bdlx.qiniu_sync_config' doesn't exist"}
[2025-09-05 23:19:42] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:19:42] INFO: 开始安装七牛云同步插件... 
[2025-09-05 23:19:42] INFO: 配置更新成功 {"key":"security.encryption_key"}
[2025-09-05 23:19:42] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:19:42] INFO: 配置更新成功 {"key":"sync.enabled"}
[2025-09-05 23:19:42] INFO: 七牛云同步插件安装完成 
[2025-09-05 23:19:44] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:19:44] INFO: 开始安装七牛云同步插件... 
[2025-09-05 23:19:44] INFO: 配置更新成功 {"key":"security.encryption_key"}
[2025-09-05 23:19:44] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:19:44] INFO: 配置更新成功 {"key":"sync.enabled"}
[2025-09-05 23:19:44] INFO: 七牛云同步插件安装完成 
[2025-09-05 23:20:12] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:20:15] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:20:15] INFO: 开始安装七牛云同步插件... 
[2025-09-05 23:20:15] INFO: 配置更新成功 {"key":"security.encryption_key"}
[2025-09-05 23:20:15] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:20:15] INFO: 配置更新成功 {"key":"sync.enabled"}
[2025-09-05 23:20:15] INFO: 七牛云同步插件安装完成 
[2025-09-05 23:20:30] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:20:31] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:20:33] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:20:37] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:20:40] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:20:40] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:20:40] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:20:40] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:20:40] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:20:40] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:20:40] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:20:40] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:20:40] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:20:40] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:20:55] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:20:55] INFO: 七牛云同步插件已启用 
[2025-09-05 23:21:07] ERROR: 连接测试失败 {"error":"连接失败: bad token"}
[2025-09-05 23:22:26] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:22:26] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:22:26] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:22:26] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:22:26] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:22:26] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:22:26] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:22:26] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:22:26] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:23:08] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:23:08] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:23:08] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:23:08] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:23:08] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:23:08] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:23:08] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:23:08] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:23:08] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:24:01] INFO: 开始安装七牛云同步插件... 
[2025-09-05 23:24:01] INFO: 配置更新成功 {"key":"security.encryption_key"}
[2025-09-05 23:24:01] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:24:01] INFO: 配置更新成功 {"key":"sync.enabled"}
[2025-09-05 23:24:01] INFO: 七牛云同步插件安装完成 
[2025-09-05 23:24:05] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:24:05] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:24:05] INFO: 七牛云同步插件已启用 
[2025-09-05 23:24:11] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:24:41] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:24:43] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:24:44] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:24:46] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:25:09] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:25:09] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:25:09] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:25:09] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:25:09] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:25:09] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:25:09] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:25:09] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:25:09] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:25:09] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:27:11] INFO: 同步服务启动 
[2025-09-05 23:27:11] INFO: 开始监控文件变化 
[2025-09-05 23:27:11] INFO: 检测到文件变化 {"file":"uploads\/images\/FXT_20250829_TBW2A.png","action":"created"}
[2025-09-05 23:27:11] INFO: 任务添加到队列 {"job_id":"1","file_path":"uploads\/images\/FXT_20250829_TBW2A.png","action":"upload","priority":0}
[2025-09-05 23:27:11] INFO: 检测到文件变化 {"file":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"created"}
[2025-09-05 23:27:11] INFO: 任务添加到队列 {"job_id":"2","file_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"upload","priority":0}
[2025-09-05 23:29:11] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:29:11] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:29:11] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:29:11] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:29:11] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:29:11] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:29:11] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:29:11] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:29:11] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:29:11] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:29:11] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:29:11] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:29:11] INFO: 同步服务启动 
[2025-09-05 23:29:11] INFO: 开始监控文件变化 
[2025-09-05 23:29:11] INFO: 检测到文件变化 {"file":"uploads\/images\/FXT_20250829_TBW2A.png","action":"created"}
[2025-09-05 23:29:11] INFO: 任务添加到队列 {"job_id":"3","file_path":"uploads\/images\/FXT_20250829_TBW2A.png","action":"upload","priority":0}
[2025-09-05 23:29:11] INFO: 检测到文件变化 {"file":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"created"}
[2025-09-05 23:29:11] INFO: 任务添加到队列 {"job_id":"4","file_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"upload","priority":0}
[2025-09-05 23:29:29] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:29:29] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:29:29] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:29:29] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:29:29] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:29:29] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:29:29] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:29:29] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:29:29] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:33:30] INFO: 开始安装七牛云同步插件... 
[2025-09-05 23:33:30] INFO: 配置更新成功 {"key":"security.encryption_key"}
[2025-09-05 23:33:30] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:33:30] INFO: 配置更新成功 {"key":"sync.enabled"}
[2025-09-05 23:33:30] INFO: 七牛云同步插件安装完成 
[2025-09-05 23:33:33] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:33:33] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:33:33] INFO: 七牛云同步插件已启用 
[2025-09-05 23:33:35] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:33:35] INFO: 配置更新成功 {"key":"plugin.enabled"}
[2025-09-05 23:33:35] INFO: 七牛云同步插件已启用 
[2025-09-05 23:33:46] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:34:00] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:34:16] WARNING: 七牛云配置不完整，部分功能将不可用 
[2025-09-05 23:34:16] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:34:16] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:34:16] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:34:16] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:34:16] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:34:16] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:34:16] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:34:16] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:34:16] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:35:06] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:35:09] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:35:21] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:35:27] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:35:53] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:38:26] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:38:26] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:38:26] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:38:26] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:38:26] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:38:26] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:38:26] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:38:26] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:38:26] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:39:13] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:39:21] INFO: 同步服务启动 
[2025-09-05 23:39:21] INFO: 开始监控文件变化 
[2025-09-05 23:39:21] INFO: 检测到文件变化 {"file":"uploads\/images\/FXT_20250829_TBW2A.png","action":"created"}
[2025-09-05 23:39:21] INFO: 任务添加到队列 {"job_id":"5","file_path":"uploads\/images\/FXT_20250829_TBW2A.png","action":"upload","priority":0}
[2025-09-05 23:39:21] INFO: 检测到文件变化 {"file":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"created"}
[2025-09-05 23:39:21] INFO: 任务添加到队列 {"job_id":"6","file_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"upload","priority":0}
[2025-09-05 23:41:21] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:41:21] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:41:21] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:41:21] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:41:21] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:41:21] INFO: 同步服务已停止 
[2025-09-05 23:41:21] INFO: 同步服务启动 
[2025-09-05 23:41:21] INFO: 开始监控文件变化 
[2025-09-05 23:41:21] INFO: 检测到文件变化 {"file":"uploads\/images\/FXT_20250829_TBW2A.png","action":"created"}
[2025-09-05 23:41:21] INFO: 任务添加到队列 {"job_id":"7","file_path":"uploads\/images\/FXT_20250829_TBW2A.png","action":"upload","priority":0}
[2025-09-05 23:41:21] INFO: 检测到文件变化 {"file":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"created"}
[2025-09-05 23:41:21] INFO: 任务添加到队列 {"job_id":"8","file_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"upload","priority":0}
[2025-09-05 23:43:41] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:44:03] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:45:07] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:45:07] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:45:07] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:45:07] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:45:07] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:45:07] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:45:07] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:45:07] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:45:07] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:48:46] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:48:46] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:48:46] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:48:46] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:48:46] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:48:46] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:48:46] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:48:46] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:48:46] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:49:33] INFO: 配置更新成功 {"key":"qiniu.access_key"}
[2025-09-05 23:49:33] INFO: 配置更新成功 {"key":"qiniu.secret_key"}
[2025-09-05 23:49:33] INFO: 配置更新成功 {"key":"qiniu.bucket"}
[2025-09-05 23:49:33] INFO: 配置更新成功 {"key":"qiniu.domain"}
[2025-09-05 23:49:33] INFO: 配置更新成功 {"key":"qiniu.prefix"}
[2025-09-05 23:49:33] INFO: 配置更新成功 {"key":"qiniu.region"}
[2025-09-05 23:49:33] INFO: 配置更新成功 {"key":"qiniu.use_https"}
[2025-09-05 23:49:33] INFO: 配置更新成功 {"key":"qiniu.use_cdn_domain"}
[2025-09-05 23:49:33] INFO: 批量配置更新成功 {"count":8}
[2025-09-05 23:49:39] INFO: 同步服务启动 
[2025-09-05 23:49:39] INFO: 开始监控文件变化 
[2025-09-05 23:49:39] INFO: 检测到文件变化 {"file":"uploads\/images\/FXT_20250829_TBW2A.png","action":"created"}
[2025-09-05 23:49:39] INFO: 任务添加到队列 {"job_id":"9","file_path":"uploads\/images\/FXT_20250829_TBW2A.png","action":"upload","priority":0}
[2025-09-05 23:49:39] INFO: 检测到文件变化 {"file":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"created"}
[2025-09-05 23:49:39] INFO: 任务添加到队列 {"job_id":"10","file_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"upload","priority":0}
[2025-09-05 23:51:39] INFO: 开始手动同步所有文件 
[2025-09-05 23:51:39] INFO: 文件记录成功 {"file_id":"1","local_path":"uploads\/images\/FXT_20250829_TBW2A.png","qiniu_key":"BDLXIMG\/uploads\/images\/FXT_20250829_TBW2A.png"}
[2025-09-05 23:51:39] INFO: 文件同步状态更新 {"file_id":"1","status":"success","qiniu_url":"https:\/\/img.seeweb3.cn\/BDLXIMG\/uploads\/images\/FXT_20250829_TBW2A.png"}
[2025-09-05 23:51:39] INFO: 文件上传成功 {"file_id":"1","local_path":"uploads\/images\/FXT_20250829_TBW2A.png","qiniu_key":"BDLXIMG\/uploads\/images\/FXT_20250829_TBW2A.png","qiniu_url":"https:\/\/img.seeweb3.cn\/BDLXIMG\/uploads\/images\/FXT_20250829_TBW2A.png","file_size":833395}
[2025-09-05 23:51:39] INFO: 文件记录成功 {"file_id":"2","local_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","qiniu_key":"BDLXIMG\/uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png"}
[2025-09-05 23:51:39] INFO: 文件同步状态更新 {"file_id":"2","status":"success","qiniu_url":"https:\/\/img.seeweb3.cn\/BDLXIMG\/uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png"}
[2025-09-05 23:51:39] INFO: 文件上传成功 {"file_id":"2","local_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","qiniu_key":"BDLXIMG\/uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","qiniu_url":"https:\/\/img.seeweb3.cn\/BDLXIMG\/uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","file_size":2388}
[2025-09-05 23:51:39] INFO: 手动同步完成 {"total_files":2,"processed_files":2,"success_count":2,"fail_count":0}
[2025-09-05 23:51:39] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:51:39] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:51:39] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:53:24] INFO: 同步服务启动 
[2025-09-05 23:53:24] INFO: 开始监控文件变化 
[2025-09-05 23:53:24] INFO: 检测到文件变化 {"file":"uploads\/images\/FXT_20250829_TBW2A.png","action":"created"}
[2025-09-05 23:53:24] INFO: 任务添加到队列 {"job_id":"11","file_path":"uploads\/images\/FXT_20250829_TBW2A.png","action":"upload","priority":0}
[2025-09-05 23:53:24] INFO: 检测到文件变化 {"file":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"created"}
[2025-09-05 23:53:24] INFO: 任务添加到队列 {"job_id":"12","file_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"upload","priority":0}
[2025-09-05 23:55:25] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:55:25] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:55:25] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:55:30] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:57:01] INFO: 同步服务启动 
[2025-09-05 23:57:01] INFO: 开始监控文件变化 
[2025-09-05 23:57:01] INFO: 检测到文件变化 {"file":"uploads\/images\/FXT_20250829_TBW2A.png","action":"created"}
[2025-09-05 23:57:01] INFO: 任务添加到队列 {"job_id":"13","file_path":"uploads\/images\/FXT_20250829_TBW2A.png","action":"upload","priority":0}
[2025-09-05 23:57:01] INFO: 检测到文件变化 {"file":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"created"}
[2025-09-05 23:57:01] INFO: 任务添加到队列 {"job_id":"14","file_path":"uploads\/images\/Thumbsimgs\/FXT_20250829_TBW2A_thumb.png","action":"upload","priority":0}
[2025-09-05 23:59:01] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:59:01] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:59:01] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:59:01] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:59:01] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:59:01] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
[2025-09-05 23:59:01] ERROR: 获取待同步文件失败 {"error":"SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ''50'' at line 5"}
