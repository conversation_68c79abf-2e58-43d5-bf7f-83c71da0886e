<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试扫描超时问题</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <h2>测试扫描超时问题</h2>
        
        <div class="alert alert-info">
            <h5>问题诊断</h5>
            <p>如果扫描一直转圈或超时，可能的原因：</p>
            <ul>
                <li>目录中文件太多，扫描耗时过长</li>
                <li>网络请求超时</li>
                <li>PHP执行时间限制</li>
                <li>数据库操作缓慢</li>
            </ul>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>快速测试</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-primary w-100 mb-2" onclick="testQuickScan()">
                            <i class="bi bi-lightning"></i> 快速扫描（限制50个文件）
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-secondary w-100 mb-2" onclick="testDirectoryInfo()">
                            <i class="bi bi-info-circle"></i> 检查目录信息
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-warning w-100 mb-2" onclick="testOriginalScan()">
                            <i class="bi bi-clock"></i> 原始扫描（可能超时）
                        </button>
                    </div>
                </div>
                
                <div class="mt-3">
                    <label for="testDir" class="form-label">测试目录路径:</label>
                    <input type="text" class="form-control" id="testDir" 
                           value="D:/xampp/htdocs/BDLX/uploads/images" 
                           placeholder="输入要测试的目录路径">
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>测试结果</h5>
            </div>
            <div class="card-body">
                <div id="test-results" style="background: #f8f9fa; padding: 15px; border-radius: 5px; min-height: 200px; max-height: 400px; overflow-y: auto;">
                    点击上面的按钮开始测试...
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>解决方案</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>如果快速扫描成功：</h6>
                        <ul>
                            <li>使用快速扫描功能</li>
                            <li>分批处理大目录</li>
                            <li>限制每次扫描的文件数量</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>如果仍然超时：</h6>
                        <ul>
                            <li>检查目录权限</li>
                            <li>减少目录中的文件数量</li>
                            <li>使用更小的批次大小</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="index.php" class="btn btn-success">返回管理面板</a>
                    <a href="files.php" class="btn btn-primary">文件管理</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResult(message, isSuccess = false) {
            const resultDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const color = isSuccess ? 'text-success' : (message.includes('❌') ? 'text-danger' : 'text-info');
            resultDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function testQuickScan() {
            showResult('开始快速扫描测试...', true);
            
            const startTime = Date.now();
            
            fetch('quick_scan.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'quick_scan'})
            })
            .then(response => {
                const duration = ((Date.now() - startTime) / 1000).toFixed(2);
                showResult(`请求完成，耗时: ${duration}秒`, true);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showResult(`✅ 快速扫描成功！发现 ${data.total || 0} 个文件，添加 ${data.added || 0} 个新文件`, true);
                    
                    if (data.scanned_dirs) {
                        data.scanned_dirs.forEach(dir => {
                            showResult(`  📁 ${dir.path}: ${dir.total} 个文件 (${dir.exists ? '存在' : '不存在'})`, true);
                        });
                    }
                } else {
                    showResult('⚠️ 快速扫描失败: ' + (data.message || data.error || '未知错误'));
                }
            })
            .catch(error => {
                showResult('❌ 快速扫描请求失败: ' + error.message);
            });
        }

        function testDirectoryInfo() {
            const testDir = document.getElementById('testDir').value;
            if (!testDir) {
                showResult('❌ 请输入测试目录路径');
                return;
            }
            
            showResult(`检查目录信息: ${testDir}`, true);
            
            fetch('quick_scan.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'test_scan', directory: testDir})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(`✅ 目录信息: ${data.message}`, true);
                    showResult(`  📊 文件数: ${data.file_count}, 子目录数: ${data.dir_count}`, true);
                } else {
                    showResult('⚠️ 目录检查失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                showResult('❌ 目录检查请求失败: ' + error.message);
            });
        }

        function testOriginalScan() {
            showResult('开始原始扫描测试（可能会超时）...', true);
            showResult('⏰ 如果超过30秒没有响应，说明确实存在超时问题');
            
            const startTime = Date.now();
            const timeoutId = setTimeout(() => {
                showResult('❌ 请求超时（30秒），确认存在超时问题');
            }, 30000);
            
            fetch('scan_files.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'scan_local_files'})
            })
            .then(response => {
                clearTimeout(timeoutId);
                const duration = ((Date.now() - startTime) / 1000).toFixed(2);
                showResult(`原始扫描请求完成，耗时: ${duration}秒`, true);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showResult(`✅ 原始扫描成功！发现 ${data.total || 0} 个文件，添加 ${data.added || 0} 个新文件`, true);
                } else {
                    showResult('⚠️ 原始扫描失败: ' + (data.message || data.error || '未知错误'));
                }
            })
            .catch(error => {
                clearTimeout(timeoutId);
                showResult('❌ 原始扫描请求失败: ' + error.message);
            });
        }
    </script>
</body>
</html>
