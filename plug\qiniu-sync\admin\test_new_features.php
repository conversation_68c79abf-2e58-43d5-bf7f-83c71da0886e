<?php
/**
 * 测试新功能
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

echo "<h2>测试新功能</h2>";

try {
    $qiniuSync = new QiniuSync();
    $configManager = $qiniuSync->getConfigManager();
    $fileManager = $qiniuSync->getFileManager();
    
    echo "<h3>1. 测试同步目录配置</h3>";
    
    // 测试保存同步目录配置
    $testDirs = [
        QINIU_SYNC_ROOT . '/uploads',
        QINIU_SYNC_ROOT . '/images',
        dirname(__DIR__) . '/test_files'
    ];
    
    echo "<h4>保存测试目录配置:</h4>";
    foreach ($testDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "<p>✓ 创建目录: $dir</p>";
        } else {
            echo "<p>✓ 目录已存在: $dir</p>";
        }
    }
    
    // 保存配置
    $configManager->updateConfig('sync.directories', $testDirs);
    $configManager->updateConfig('sync.allowed_extensions', ['jpg','jpeg','png','gif','txt','pdf']);
    $configManager->updateConfig('sync.max_file_size', 10);
    
    echo "<p>✓ 配置已保存</p>";
    
    // 读取配置验证
    $savedDirs = $configManager->getConfig('sync.directories', []);
    echo "<h4>已保存的同步目录:</h4>";
    echo "<ul>";
    foreach ($savedDirs as $dir) {
        echo "<li>" . htmlspecialchars($dir) . "</li>";
    }
    echo "</ul>";
    
    echo "<h3>2. 测试文件扫描功能</h3>";
    
    // 创建一些测试文件
    $testFiles = [
        $testDirs[2] . '/scan_test1.txt' => 'Scan test file 1',
        $testDirs[2] . '/scan_test2.jpg' => 'Fake image content',
        $testDirs[2] . '/scan_test3.pdf' => 'Fake PDF content'
    ];
    
    echo "<h4>创建测试文件:</h4>";
    foreach ($testFiles as $filePath => $content) {
        file_put_contents($filePath, $content);
        echo "<p>✓ 创建文件: " . basename($filePath) . "</p>";
    }
    
    // 测试扫描功能
    echo "<h4>扫描结果:</h4>";
    $totalScanned = 0;
    $totalAdded = 0;
    
    foreach ($savedDirs as $dir) {
        if (is_dir($dir)) {
            $result = $fileManager->scanAndAddDirectory($dir, true);
            if ($result) {
                echo "<p>目录 " . basename($dir) . ": 发现 {$result['total']} 个文件，添加 {$result['success']} 个</p>";
                $totalScanned += $result['total'];
                $totalAdded += $result['success'];
            }
        }
    }
    
    echo "<p><strong>总计: 扫描 $totalScanned 个文件，添加 $totalAdded 个到同步队列</strong></p>";
    
    echo "<h3>3. 测试文件列表筛选</h3>";
    
    // 测试按目录筛选
    echo "<h4>按目录筛选测试:</h4>";
    foreach ($savedDirs as $dir) {
        $files = $fileManager->getFiles(1, 10, 'all', $dir);
        $count = $fileManager->getTotalFiles('all', $dir);
        echo "<p>目录 " . basename($dir) . ": $count 个文件</p>";
        
        if (!empty($files)) {
            echo "<ul>";
            foreach (array_slice($files, 0, 3) as $file) {
                echo "<li>" . htmlspecialchars(basename($file['local_path'])) . " (" . $file['sync_status'] . ")</li>";
            }
            if (count($files) > 3) {
                echo "<li>... 还有 " . (count($files) - 3) . " 个文件</li>";
            }
            echo "</ul>";
        }
    }
    
    echo "<h3>4. 数据库统计</h3>";
    $stats = $fileManager->getStats();
    echo "<p>总文件数: " . $stats['total'] . "</p>";
    echo "<p>已同步: " . $stats['synced'] . "</p>";
    echo "<p>待同步: " . $stats['pending'] . "</p>";
    echo "<p>失败: " . $stats['failed'] . "</p>";
    
    echo "<hr>";
    echo "<h3>测试链接</h3>";
    echo "<p><a href='index.php' target='_blank'>管理面板 - 配置设置</a></p>";
    echo "<p><a href='files.php' target='_blank'>文件管理 - 新的筛选功能</a></p>";
    echo "<p><a href='test_status.php' target='_blank'>同步状态测试</a></p>";
    
} catch (Exception $e) {
    echo "<p>✗ 错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
