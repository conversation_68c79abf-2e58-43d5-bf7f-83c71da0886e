<?php
/**
 * 调试版本的AJAX处理器
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

try {
    // 引入插件核心文件
    require_once dirname(__DIR__) . '/bootstrap.php';
    
    // 检查会话
    session_start();
    
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    // 调试信息
    $debug = [
        'session_status' => session_status(),
        'session_data' => $_SESSION ?? [],
        'input_data' => $input,
        'parsed_data' => $data,
        'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
    ];
    
    if (!$data || !isset($data['action'])) {
        echo json_encode([
            'success' => false, 
            'error' => '无效的请求',
            'debug' => $debug
        ]);
        exit;
    }
    
    $action = $data['action'];
    
    // 创建插件实例
    $qiniuSync = new QiniuSync();
    
    $result = ['success' => false, 'message' => '未知操作'];
    
    switch ($action) {
        case 'save_config':
            // 简化的配置保存测试
            $result = [
                'success' => true,
                'message' => '配置保存成功（调试模式）',
                'received_data' => $data
            ];
            break;
            
        case 'save_sync_dirs':
            // 简化的目录配置保存测试
            $result = [
                'success' => true,
                'message' => '目录配置保存成功（调试模式）',
                'received_data' => $data
            ];
            break;
            
        case 'validate_config':
            // 简化的配置验证测试
            $result = [
                'success' => true,
                'message' => '配置验证成功（调试模式）'
            ];
            break;
            
        default:
            $result = [
                'success' => false,
                'message' => '未知操作: ' . $action,
                'debug' => $debug
            ];
    }
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'debug' => $debug ?? []
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'error' => 'PHP Error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'debug' => $debug ?? []
    ], JSON_UNESCAPED_UNICODE);
}
?>
