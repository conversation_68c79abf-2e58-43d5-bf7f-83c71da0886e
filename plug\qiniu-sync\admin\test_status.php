<?php
/**
 * 测试同步状态
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

echo "<h2>同步状态测试</h2>";

try {
    $qiniuSync = new QiniuSync();
    
    echo "<h3>1. 当前状态</h3>";
    $status = $qiniuSync->getStatus();
    echo "<pre>" . json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
    echo "<h3>2. 配置管理器状态</h3>";
    $configManager = $qiniuSync->getConfigManager();
    $syncEnabled = $configManager->getConfig('sync.enabled', false);
    $lastStartTime = $configManager->getConfig('sync.last_start_time', null);
    
    echo "<p>sync.enabled: " . ($syncEnabled ? 'true' : 'false') . "</p>";
    echo "<p>sync.last_start_time: " . ($lastStartTime ?: '未设置') . "</p>";
    
    echo "<h3>3. 同步管理器状态</h3>";
    $syncManager = $qiniuSync->getSyncManager();
    $isRunning = $syncManager->isRunning();
    echo "<p>SyncManager.isRunning(): " . ($isRunning ? 'true' : 'false') . "</p>";
    
    echo "<h3>4. 测试启动/停止</h3>";
    echo "<button onclick='testStart()'>测试启动</button> ";
    echo "<button onclick='testStop()'>测试停止</button> ";
    echo "<button onclick='checkStatus()'>检查状态</button>";
    echo "<div id='result' style='margin-top: 10px; padding: 10px; border: 1px solid #ccc;'></div>";
    
} catch (Exception $e) {
    echo "<p>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<script>
function showResult(message) {
    document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(message, null, 2) + '</pre>';
}

function testStart() {
    fetch('ajax.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({action: 'start_sync'})
    })
    .then(response => response.json())
    .then(data => {
        showResult({action: 'start_sync', result: data});
        // 启动后检查状态
        setTimeout(checkStatus, 1000);
    })
    .catch(error => showResult({error: error.message}));
}

function testStop() {
    fetch('ajax.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({action: 'stop_sync'})
    })
    .then(response => response.json())
    .then(data => {
        showResult({action: 'stop_sync', result: data});
        // 停止后检查状态
        setTimeout(checkStatus, 1000);
    })
    .catch(error => showResult({error: error.message}));
}

function checkStatus() {
    fetch('ajax.php', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({action: 'get_status'})
    })
    .then(response => response.json())
    .then(data => {
        showResult({action: 'get_status', result: data});
    })
    .catch(error => showResult({error: error.message}));
}
</script>

<style>
button {
    padding: 8px 16px;
    margin: 4px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
button:hover {
    background: #0056b3;
}
</style>
