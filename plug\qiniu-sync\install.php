<?php
/**
 * 七牛云同步插件安装程序
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 引入插件核心文件
require_once __DIR__ . '/bootstrap.php';

// 检查是否为命令行模式
$isCliMode = php_sapi_name() === 'cli';

// 如果是Web模式，检查权限
if (!$isCliMode) {
    session_start();
    if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
        header('Location: ../../admin/login.php');
        exit;
    }
}

// 处理AJAX请求
if (!$isCliMode && $_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data || !isset($data['action'])) {
            throw new Exception('无效的请求');
        }
        
        $action = $data['action'];
        
        switch ($action) {
            case 'install':
                $result = installPlugin();
                break;
            case 'uninstall':
                $result = uninstallPlugin();
                break;
            case 'enable':
                $result = enablePlugin();
                break;
            case 'disable':
                $result = disablePlugin();
                break;
            case 'test_dependencies':
                $result = testDependencies();
                break;
            case 'get_status':
                $result = getPluginStatus();
                break;
            default:
                $result = ['success' => false, 'message' => '未知操作'];
        }
        
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 命令行模式处理
if ($isCliMode) {
    $action = $argv[1] ?? 'help';
    
    switch ($action) {
        case 'install':
            $result = installPlugin();
            break;
        case 'uninstall':
            $result = uninstallPlugin();
            break;
        case 'enable':
            $result = enablePlugin();
            break;
        case 'disable':
            $result = disablePlugin();
            break;
        case 'status':
            $result = getPluginStatus();
            break;
        case 'test':
            $result = testDependencies();
            break;
        default:
            showHelp();
            exit;
    }
    
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    exit;
}

/**
 * 安装插件
 */
function installPlugin()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->install();
    } catch (Exception $e) {
        return ['success' => false, 'message' => '安装失败: ' . $e->getMessage()];
    }
}

/**
 * 卸载插件
 */
function uninstallPlugin()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->uninstall();
    } catch (Exception $e) {
        return ['success' => false, 'message' => '卸载失败: ' . $e->getMessage()];
    }
}

/**
 * 启用插件
 */
function enablePlugin()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->enable();
    } catch (Exception $e) {
        return ['success' => false, 'message' => '启用失败: ' . $e->getMessage()];
    }
}

/**
 * 禁用插件
 */
function disablePlugin()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->disable();
    } catch (Exception $e) {
        return ['success' => false, 'message' => '禁用失败: ' . $e->getMessage()];
    }
}

/**
 * 测试依赖
 */
function testDependencies()
{
    $results = [];
    
    // 检查PHP版本
    $phpVersion = PHP_VERSION;
    $results['php_version'] = [
        'required' => '7.2.0',
        'current' => $phpVersion,
        'status' => version_compare($phpVersion, '7.2.0', '>=')
    ];
    
    // 检查必要扩展
    $requiredExtensions = ['curl', 'json', 'mbstring', 'openssl', 'pdo', 'pdo_mysql'];
    foreach ($requiredExtensions as $ext) {
        $results['extensions'][$ext] = [
            'required' => true,
            'status' => extension_loaded($ext)
        ];
    }
    
    // 检查七牛云SDK
    $results['qiniu_sdk'] = [
        'required' => true,
        'status' => class_exists('Qiniu\Auth')
    ];
    
    // 检查目录权限
    $directories = [
        QINIU_SYNC_PATH . '/logs',
        QINIU_SYNC_PATH . '/cache',
        QINIU_SYNC_PATH . '/temp'
    ];
    
    foreach ($directories as $dir) {
        $results['directories'][basename($dir)] = [
            'path' => $dir,
            'exists' => is_dir($dir),
            'writable' => is_writable($dir)
        ];
    }
    
    // 检查数据库连接
    try {
        $dbConfigFile = QINIU_SYNC_PATH . '/../../config.php';
        if (file_exists($dbConfigFile)) {
            include $dbConfigFile;
            $pdo = new PDO(
                "mysql:host={$db_host};dbname={$db_name};charset=utf8mb4",
                $db_user,
                $db_pass,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            $results['database'] = [
                'status' => true,
                'message' => '数据库连接正常'
            ];
        } else {
            $results['database'] = [
                'status' => false,
                'message' => '数据库配置文件不存在'
            ];
        }
    } catch (Exception $e) {
        $results['database'] = [
            'status' => false,
            'message' => '数据库连接失败: ' . $e->getMessage()
        ];
    }
    
    // 计算总体状态
    $allPassed = true;
    
    if (!$results['php_version']['status']) {
        $allPassed = false;
    }
    
    foreach ($results['extensions'] as $ext => $info) {
        if (!$info['status']) {
            $allPassed = false;
            break;
        }
    }
    
    if (!$results['qiniu_sdk']['status']) {
        $allPassed = false;
    }
    
    if (!$results['database']['status']) {
        $allPassed = false;
    }
    
    return [
        'success' => $allPassed,
        'message' => $allPassed ? '所有依赖检查通过' : '存在依赖问题',
        'details' => $results
    ];
}

/**
 * 获取插件状态
 */
function getPluginStatus()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->getStatus();
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '获取状态失败: ' . $e->getMessage(),
            'data' => [
                'installed' => false,
                'enabled' => false,
                'sync_running' => false
            ]
        ];
    }
}

/**
 * 显示帮助信息
 */
function showHelp()
{
    echo "七牛云同步插件管理工具\n\n";
    echo "用法: php install.php [命令]\n\n";
    echo "可用命令:\n";
    echo "  install    安装插件\n";
    echo "  uninstall  卸载插件\n";
    echo "  enable     启用插件\n";
    echo "  disable    禁用插件\n";
    echo "  status     查看插件状态\n";
    echo "  test       测试依赖\n";
    echo "  help       显示帮助信息\n\n";
}

// Web界面
if (!$isCliMode):
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>七牛云同步插件 - 安装程序</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .step-card {
            border-left: 4px solid #667eea;
        }
        
        .btn-primary {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
            border-color: #5a6fd8;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cloud-upload"></i> 七牛云同步插件 - 安装程序
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../../admin/">返回主面板</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- 插件信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle"></i> 插件信息
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>插件名称:</strong> <?php echo QINIU_SYNC_NAME; ?></p>
                                <p><strong>版本:</strong> <?php echo QINIU_SYNC_VERSION; ?></p>
                                <p><strong>描述:</strong> <?php echo QINIU_SYNC_DESCRIPTION; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>作者:</strong> BDLX Team</p>
                                <p><strong>PHP版本要求:</strong> >= 7.2</p>
                                <p><strong>依赖:</strong> 七牛云PHP SDK</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 当前状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-speedometer2"></i> 当前状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="plugin-status">
                            <p>正在检查状态...</p>
                        </div>
                        <button class="btn btn-secondary btn-sm" onclick="checkStatus()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新状态
                        </button>
                    </div>
                </div>

                <!-- 依赖检查 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle"></i> 依赖检查
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="dependencies-check">
                            <p>点击下方按钮检查依赖...</p>
                        </div>
                        <button class="btn btn-info" onclick="testDependencies()">
                            <i class="bi bi-search"></i> 检查依赖
                        </button>
                    </div>
                </div>

                <!-- 操作面板 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i> 操作面板
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="step-card card h-100">
                                    <div class="card-body">
                                        <h6>安装插件</h6>
                                        <p class="text-muted">创建数据库表，初始化配置</p>
                                        <button class="btn btn-primary" onclick="installPlugin()">
                                            <i class="bi bi-download"></i> 安装插件
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="step-card card h-100">
                                    <div class="card-body">
                                        <h6>启用插件</h6>
                                        <p class="text-muted">启用插件功能</p>
                                        <button class="btn btn-success" onclick="enablePlugin()">
                                            <i class="bi bi-play"></i> 启用插件
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="step-card card h-100">
                                    <div class="card-body">
                                        <h6>禁用插件</h6>
                                        <p class="text-muted">禁用插件功能</p>
                                        <button class="btn btn-warning" onclick="disablePlugin()">
                                            <i class="bi bi-pause"></i> 禁用插件
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="step-card card h-100">
                                    <div class="card-body">
                                        <h6>卸载插件</h6>
                                        <p class="text-muted">删除数据库表和配置</p>
                                        <button class="btn btn-danger" onclick="uninstallPlugin()">
                                            <i class="bi bi-trash"></i> 卸载插件
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3 text-center">
                            <a href="admin/index.php" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-right"></i> 进入管理面板
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 操作结果 -->
                <div class="card" id="result-card" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle"></i> 操作结果
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="operation-result"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">正在处理，请稍候...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            // 添加超时保护，确保加载模态框不会永远显示
            setTimeout(() => {
                try {
                    if (loadingModal._isShown || document.querySelector('.modal.show')) {
                        hideLoading();
                        showResult('请求超时，请刷新页面重试', false);
                    }
                } catch (e) {
                    console.error('超时检查错误:', e);
                }
            }, 8000); // 8秒超时

            // 延迟执行状态检查，确保页面完全加载
            setTimeout(() => {
                checkStatus();
            }, 500);
        });

        function showLoading() {
            loadingModal.show();
        }

        function hideLoading() {
            try {
                loadingModal.hide();

                // 强制隐藏所有模态框
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                });

                // 移除背景遮罩
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());

                // 恢复body滚动
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';

            } catch (e) {
                console.error('隐藏加载框错误:', e);
            }
        }

        function showResult(message, success = true) {
            const resultCard = document.getElementById('result-card');
            const resultDiv = document.getElementById('operation-result');
            
            resultDiv.innerHTML = `
                <div class="alert alert-${success ? 'success' : 'danger'}" role="alert">
                    <i class="bi bi-${success ? 'check-circle' : 'exclamation-triangle'}"></i>
                    ${message}
                </div>
            `;
            
            resultCard.style.display = 'block';
            resultCard.scrollIntoView({ behavior: 'smooth' });
        }

        function sendRequest(action, data = {}) {
            showLoading();

            return fetch('install.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action: action, ...data })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(text => {
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('服务器响应:', text);
                    throw new Error('服务器返回了无效的JSON响应');
                }
            })
            .then(result => {
                hideLoading();
                return result;
            })
            .catch(error => {
                hideLoading();
                console.error('请求错误:', error);
                showResult('请求失败: ' + error.message, false);
                throw error;
            });
        }

        function checkStatus() {
            sendRequest('get_status')
                .then(result => {
                    const statusDiv = document.getElementById('plugin-status');

                    if (result.success) {
                        const data = result.data;
                        statusDiv.innerHTML = `
                            <div class="row">
                                <div class="col-md-6">
                                    <p>
                                        <span class="status-indicator ${data.installed ? 'status-success' : 'status-error'}"></span>
                                        <strong>安装状态:</strong> ${data.installed ? '已安装' : '未安装'}
                                    </p>
                                    <p>
                                        <span class="status-indicator ${data.enabled ? 'status-success' : 'status-error'}"></span>
                                        <strong>启用状态:</strong> ${data.enabled ? '已启用' : '已禁用'}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p>
                                        <span class="status-indicator ${data.sync_running ? 'status-success' : 'status-error'}"></span>
                                        <strong>同步状态:</strong> ${data.sync_running ? '运行中' : '已停止'}
                                    </p>
                                    <p><strong>队列大小:</strong> ${data.queue_size ? data.queue_size.total || 0 : 0}</p>
                                </div>
                            </div>
                        `;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                无法获取插件状态: ${result.message}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    const statusDiv = document.getElementById('plugin-status');
                    statusDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            状态检查失败: ${error.message}
                        </div>
                    `;
                });
        }

        function testDependencies() {
            sendRequest('test_dependencies')
                .then(result => {
                    const checkDiv = document.getElementById('dependencies-check');
                    
                    if (result.success) {
                        checkDiv.innerHTML = `
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i>
                                所有依赖检查通过！
                            </div>
                        `;
                    } else {
                        let html = `
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                存在依赖问题，请检查以下项目：
                            </div>
                            <div class="table-responsive">
                                <table class="table table-sm">
                        `;
                        
                        const details = result.details;
                        
                        // PHP版本
                        html += `
                            <tr>
                                <td>PHP版本</td>
                                <td>${details.php_version.current}</td>
                                <td>
                                    <span class="status-indicator ${details.php_version.status ? 'status-success' : 'status-error'}"></span>
                                    ${details.php_version.status ? '通过' : '需要 >= ' + details.php_version.required}
                                </td>
                            </tr>
                        `;
                        
                        // 扩展
                        for (const [ext, info] of Object.entries(details.extensions)) {
                            html += `
                                <tr>
                                    <td>PHP扩展: ${ext}</td>
                                    <td>-</td>
                                    <td>
                                        <span class="status-indicator ${info.status ? 'status-success' : 'status-error'}"></span>
                                        ${info.status ? '已安装' : '未安装'}
                                    </td>
                                </tr>
                            `;
                        }
                        
                        // 七牛云SDK
                        html += `
                            <tr>
                                <td>七牛云SDK</td>
                                <td>-</td>
                                <td>
                                    <span class="status-indicator ${details.qiniu_sdk.status ? 'status-success' : 'status-error'}"></span>
                                    ${details.qiniu_sdk.status ? '已安装' : '未安装'}
                                </td>
                            </tr>
                        `;
                        
                        // 数据库
                        html += `
                            <tr>
                                <td>数据库连接</td>
                                <td>-</td>
                                <td>
                                    <span class="status-indicator ${details.database.status ? 'status-success' : 'status-error'}"></span>
                                    ${details.database.message}
                                </td>
                            </tr>
                        `;
                        
                        html += '</table></div>';
                        checkDiv.innerHTML = html;
                    }
                });
        }

        function installPlugin() {
            if (!confirm('确定要安装插件吗？')) return;
            
            sendRequest('install')
                .then(result => {
                    showResult(result.message, result.success);
                    if (result.success) {
                        setTimeout(checkStatus, 1000);
                    }
                });
        }

        function uninstallPlugin() {
            if (!confirm('确定要卸载插件吗？这将删除所有相关数据！')) return;
            
            sendRequest('uninstall')
                .then(result => {
                    showResult(result.message, result.success);
                    if (result.success) {
                        setTimeout(checkStatus, 1000);
                    }
                });
        }

        function enablePlugin() {
            sendRequest('enable')
                .then(result => {
                    showResult(result.message, result.success);
                    if (result.success) {
                        setTimeout(checkStatus, 1000);
                    }
                });
        }

        function disablePlugin() {
            sendRequest('disable')
                .then(result => {
                    showResult(result.message, result.success);
                    if (result.success) {
                        setTimeout(checkStatus, 1000);
                    }
                });
        }
    </script>
</body>
</html>
<?php endif; ?>
