<?php
/**
 * 队列管理页面
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 检查权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

try {
    $qiniuSync = new QiniuSync();
    $queueManager = $qiniuSync->getQueueManager();
    
    // 获取队列统计
    $rawStats = $queueManager->getQueueStats();
    $queueStats = [
        'total' => 0,
        'pending' => 0,
        'processing' => 0,
        'completed' => 0,
        'failed' => 0
    ];

    // 转换统计格式
    foreach ($rawStats as $action => $statuses) {
        foreach ($statuses as $status => $data) {
            if (isset($queueStats[$status])) {
                $queueStats[$status] += $data['count'];
                $queueStats['total'] += $data['count'];
            }
        }
    }
    
    // 获取队列任务列表
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $status = $_GET['status'] ?? 'all';
    
    $tasks = $queueManager->getTasks($page, $limit, $status);
    $totalTasks = $queueManager->getTotalTasks($status);
    $totalPages = ceil($totalTasks / $limit);
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $queueStats = [
        'total' => 0,
        'pending' => 0,
        'processing' => 0,
        'completed' => 0,
        'failed' => 0
    ];
    $tasks = [];
    $totalTasks = 0;
    $totalPages = 0;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>队列管理 - 七牛云同步插件</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-cloud-upload"></i> 七牛云同步插件
            </a>
            <span class="navbar-text">队列管理</span>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> 错误: <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- 队列统计 -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($queueStats['total']); ?></h5>
                        <p class="card-text">总任务数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center text-warning">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($queueStats['pending']); ?></h5>
                        <p class="card-text">等待中</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center text-info">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($queueStats['processing']); ?></h5>
                        <p class="card-text">处理中</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center text-success">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($queueStats['completed']); ?></h5>
                        <p class="card-text">已完成</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center text-danger">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($queueStats['failed']); ?></h5>
                        <p class="card-text">失败</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title" id="queueStatus">
                            <i class="bi bi-circle-fill text-success"></i>
                        </h5>
                        <p class="card-text">队列状态</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 队列控制 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex gap-2">
                            <select class="form-select" id="statusFilter" style="width: auto;">
                                <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>所有任务</option>
                                <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>等待中</option>
                                <option value="processing" <?php echo $status === 'processing' ? 'selected' : ''; ?>>处理中</option>
                                <option value="completed" <?php echo $status === 'completed' ? 'selected' : ''; ?>>已完成</option>
                                <option value="failed" <?php echo $status === 'failed' ? 'selected' : ''; ?>>失败</option>
                            </select>
                            <button class="btn btn-outline-primary" onclick="refreshQueue()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex gap-2 justify-content-end">
                            <button class="btn btn-success" onclick="startQueue()">
                                <i class="bi bi-play"></i> 启动队列
                            </button>
                            <button class="btn btn-warning" onclick="pauseQueue()">
                                <i class="bi bi-pause"></i> 暂停队列
                            </button>
                            <button class="btn btn-danger" onclick="clearQueue()">
                                <i class="bi bi-trash"></i> 清空队列
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-task"></i> 任务列表
                    <small class="text-muted">(共 <?php echo number_format($totalTasks); ?> 个任务)</small>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($tasks)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <p class="text-muted mt-2">暂无任务</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>任务类型</th>
                                    <th>文件路径</th>
                                    <th>状态</th>
                                    <th>进度</th>
                                    <th>创建时间</th>
                                    <th>完成时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tasks as $task): ?>
                                    <tr>
                                        <td><?php echo $task['id']; ?></td>
                                        <td>
                                            <?php
                                            $typeIcons = [
                                                'upload' => 'cloud-upload',
                                                'delete' => 'trash',
                                                'sync' => 'arrow-repeat'
                                            ];
                                            $icon = $typeIcons[$task['task_type']] ?? 'gear';
                                            ?>
                                            <i class="bi bi-<?php echo $icon; ?> me-1"></i>
                                            <?php echo ucfirst($task['task_type']); ?>
                                        </td>
                                        <td>
                                            <span title="<?php echo htmlspecialchars($task['file_path']); ?>">
                                                <?php echo htmlspecialchars(basename($task['file_path'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            switch ($task['status']) {
                                                case 'pending':
                                                    $statusClass = 'warning';
                                                    $statusText = '等待中';
                                                    break;
                                                case 'processing':
                                                    $statusClass = 'info';
                                                    $statusText = '处理中';
                                                    break;
                                                case 'completed':
                                                    $statusClass = 'success';
                                                    $statusText = '已完成';
                                                    break;
                                                case 'failed':
                                                    $statusClass = 'danger';
                                                    $statusText = '失败';
                                                    break;
                                                default:
                                                    $statusClass = 'secondary';
                                                    $statusText = '未知';
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($task['status'] === 'processing'): ?>
                                                <div class="progress" style="width: 100px;">
                                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                                         style="width: <?php echo $task['progress'] ?? 0; ?>%">
                                                        <?php echo $task['progress'] ?? 0; ?>%
                                                    </div>
                                                </div>
                                            <?php elseif ($task['status'] === 'completed'): ?>
                                                <span class="text-success">100%</span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i:s', strtotime($task['created_at'])); ?></td>
                                        <td>
                                            <?php if ($task['completed_at']): ?>
                                                <?php echo date('Y-m-d H:i:s', strtotime($task['completed_at'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($task['status'] === 'failed'): ?>
                                                    <button class="btn btn-outline-warning" onclick="retryTask(<?php echo $task['id']; ?>)">
                                                        <i class="bi bi-arrow-repeat"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <?php if (in_array($task['status'], ['pending', 'failed'])): ?>
                                                    <button class="btn btn-outline-danger" onclick="cancelTask(<?php echo $task['id']; ?>)">
                                                        <i class="bi bi-x"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-info" onclick="viewTaskDetails(<?php echo $task['id']; ?>)">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status; ?>&limit=<?php echo $limit; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 刷新队列
        function refreshQueue() {
            location.reload();
        }

        // 状态筛选
        document.getElementById('statusFilter').addEventListener('change', function() {
            const status = this.value;
            const url = new URL(window.location);
            url.searchParams.set('status', status);
            url.searchParams.set('page', '1');
            window.location = url;
        });

        // 启动队列
        function startQueue() {
            alert('功能开发中...');
        }

        // 暂停队列
        function pauseQueue() {
            alert('功能开发中...');
        }

        // 清空队列
        function clearQueue() {
            if (confirm('确定要清空所有队列任务吗？这将删除所有未完成的任务。')) {
                alert('功能开发中...');
            }
        }

        // 重试任务
        function retryTask(taskId) {
            if (confirm('确定要重试这个任务吗？')) {
                alert('功能开发中...');
            }
        }

        // 取消任务
        function cancelTask(taskId) {
            if (confirm('确定要取消这个任务吗？')) {
                alert('功能开发中...');
            }
        }

        // 查看任务详情
        function viewTaskDetails(taskId) {
            alert('功能开发中...');
        }

        // 自动刷新队列状态
        setInterval(function() {
            // 这里可以添加AJAX请求来更新队列状态
        }, 5000);
    </script>
</body>
</html>
