<?php
/**
 * 七牛云同步插件日志记录器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class QiniuSyncLogger
{
    private $config;
    private $db;
    private $logPath;
    private $logLevel;
    private $levels = [
        'debug' => 0,
        'info' => 1,
        'warning' => 2,
        'error' => 3
    ];
    
    /**
     * 构造函数
     */
    public function __construct($config = null, $db = null)
    {
        $this->config = $config ?: $GLOBALS['qiniu_sync_config']['log'];
        $this->db = $db;
        $this->logLevel = $this->config['level'] ?? 'info';
        $this->logPath = QINIU_SYNC_PATH . '/' . ($this->config['path'] ?? 'logs/');
        
        // 确保日志目录存在
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }
    
    /**
     * 记录调试信息
     */
    public function debug($message, $context = [])
    {
        $this->log('debug', $message, $context);
    }
    
    /**
     * 记录一般信息
     */
    public function info($message, $context = [])
    {
        $this->log('info', $message, $context);
    }
    
    /**
     * 记录警告信息
     */
    public function warning($message, $context = [])
    {
        $this->log('warning', $message, $context);
    }
    
    /**
     * 记录错误信息
     */
    public function error($message, $context = [])
    {
        $this->log('error', $message, $context);
    }
    
    /**
     * 记录日志
     */
    public function log($level, $message, $context = [])
    {
        if (!$this->shouldLog($level)) {
            return;
        }
        
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'message' => $message,
            'context' => $context,
            'memory' => memory_get_usage(true),
            'pid' => getmypid()
        ];
        
        // 写入文件日志
        $this->writeToFile($logEntry);
        
        // 写入数据库日志
        if ($this->db && $this->config['enabled']) {
            $this->writeToDatabase($level, $message, $context);
        }
    }
    
    /**
     * 检查是否应该记录该级别的日志
     */
    private function shouldLog($level)
    {
        if (!isset($this->levels[$level]) || !isset($this->levels[$this->logLevel])) {
            return false;
        }
        
        return $this->levels[$level] >= $this->levels[$this->logLevel];
    }
    
    /**
     * 写入文件日志
     */
    private function writeToFile($logEntry)
    {
        $logFile = $this->logPath . 'qiniu-sync-' . date('Y-m-d') . '.log';
        
        $logLine = sprintf(
            "[%s] %s: %s %s\n",
            $logEntry['timestamp'],
            $logEntry['level'],
            $logEntry['message'],
            !empty($logEntry['context']) ? json_encode($logEntry['context'], JSON_UNESCAPED_UNICODE) : ''
        );
        
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        // 检查文件大小并轮转
        $this->rotateLogFile($logFile);
    }
    
    /**
     * 写入数据库日志
     */
    private function writeToDatabase($level, $message, $context = [])
    {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO qiniu_sync_logs 
                (log_level, log_type, message, context, file_id, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $fileId = isset($context['file_id']) ? $context['file_id'] : null;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            $contextJson = !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : null;
            
            $stmt->execute([
                $level,
                $context['type'] ?? 'sync',
                $message,
                $contextJson,
                $fileId,
                $ipAddress,
                $userAgent
            ]);
        } catch (Exception $e) {
            // 数据库日志失败时写入文件日志
            error_log("Failed to write to database log: " . $e->getMessage());
        }
    }
    
    /**
     * 轮转日志文件
     */
    private function rotateLogFile($logFile)
    {
        if (!file_exists($logFile)) {
            return;
        }
        
        $maxSize = $this->config['max_size'] ?? (10 * 1024 * 1024); // 10MB
        
        if (filesize($logFile) > $maxSize) {
            $backupFile = $logFile . '.' . time();
            rename($logFile, $backupFile);
            
            // 压缩旧日志文件
            if (function_exists('gzencode')) {
                $content = file_get_contents($backupFile);
                file_put_contents($backupFile . '.gz', gzencode($content));
                unlink($backupFile);
            }
        }
        
        // 清理过期日志文件
        $this->cleanupOldLogs();
    }
    
    /**
     * 清理过期日志文件
     */
    private function cleanupOldLogs()
    {
        $maxFiles = $this->config['max_files'] ?? 30;
        $logFiles = glob($this->logPath . 'qiniu-sync-*.log*');
        
        if (count($logFiles) > $maxFiles) {
            // 按修改时间排序
            usort($logFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的文件
            $filesToDelete = array_slice($logFiles, 0, count($logFiles) - $maxFiles);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * 获取日志文件列表
     */
    public function getLogFiles()
    {
        $logFiles = glob($this->logPath . 'qiniu-sync-*.log*');
        
        $files = [];
        foreach ($logFiles as $file) {
            $files[] = [
                'name' => basename($file),
                'path' => $file,
                'size' => filesize($file),
                'modified' => filemtime($file)
            ];
        }
        
        // 按修改时间倒序排列
        usort($files, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });
        
        return $files;
    }
    
    /**
     * 读取日志文件内容
     */
    public function readLogFile($filename, $lines = 100)
    {
        $filepath = $this->logPath . $filename;
        
        if (!file_exists($filepath)) {
            throw new Exception('日志文件不存在');
        }
        
        // 读取最后N行
        $file = new SplFileObject($filepath);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        $startLine = max(0, $totalLines - $lines);
        $file->seek($startLine);
        
        $content = [];
        while (!$file->eof()) {
            $line = trim($file->current());
            if (!empty($line)) {
                $content[] = $line;
            }
            $file->next();
        }
        
        return $content;
    }
    
    /**
     * 从数据库获取日志
     */
    public function getLogsFromDatabase($filters = [], $limit = 100, $offset = 0)
    {
        if (!$this->db) {
            throw new Exception('数据库连接不可用');
        }
        
        $where = [];
        $params = [];
        
        if (!empty($filters['level'])) {
            $where[] = 'log_level = ?';
            $params[] = $filters['level'];
        }
        
        if (!empty($filters['type'])) {
            $where[] = 'log_type = ?';
            $params[] = $filters['type'];
        }
        
        if (!empty($filters['date_from'])) {
            $where[] = 'created_at >= ?';
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = 'created_at <= ?';
            $params[] = $filters['date_to'];
        }
        
        if (!empty($filters['search'])) {
            $where[] = 'message LIKE ?';
            $params[] = '%' . $filters['search'] . '%';
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        $sql = "
            SELECT * FROM qiniu_sync_logs 
            {$whereClause}
            ORDER BY created_at DESC 
            LIMIT {$limit} OFFSET {$offset}
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * 获取日志统计
     */
    public function getLogStats($dateFrom = null, $dateTo = null)
    {
        if (!$this->db) {
            return [];
        }
        
        $where = [];
        $params = [];
        
        if ($dateFrom) {
            $where[] = 'created_at >= ?';
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $where[] = 'created_at <= ?';
            $params[] = $dateTo;
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        $sql = "
            SELECT 
                log_level,
                COUNT(*) as count
            FROM qiniu_sync_logs 
            {$whereClause}
            GROUP BY log_level
        ";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        $stats = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $stats[$row['log_level']] = $row['count'];
        }
        
        return $stats;
    }
    
    /**
     * 清理过期数据库日志
     */
    public function cleanupDatabaseLogs($days = 30)
    {
        if (!$this->db) {
            return false;
        }
        
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $stmt = $this->db->prepare("DELETE FROM qiniu_sync_logs WHERE created_at < ?");
        $result = $stmt->execute([$cutoffDate]);
        
        $deletedRows = $stmt->rowCount();
        $this->info("清理过期日志记录", ['deleted_rows' => $deletedRows, 'cutoff_date' => $cutoffDate]);

        return $deletedRows;
    }

    /**
     * 获取日志列表
     */
    public function getLogs($page = 1, $limit = 50, $level = 'all', $date = null)
    {
        try {
            if (!$this->db) {
                return [];
            }

            $offset = ($page - 1) * $limit;
            $where = [];
            $params = [];

            if ($level !== 'all') {
                $where[] = "level = ?";
                $params[] = $level;
            }

            if ($date) {
                $where[] = "DATE(created_at) = ?";
                $params[] = $date;
            }

            $whereClause = empty($where) ? '' : 'WHERE ' . implode(' AND ', $where);

            $sql = "SELECT * FROM qiniu_sync_logs $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 获取总日志数
     */
    public function getTotalLogs($level = 'all', $date = null)
    {
        try {
            if (!$this->db) {
                return 0;
            }

            $where = [];
            $params = [];

            if ($level !== 'all') {
                $where[] = "level = ?";
                $params[] = $level;
            }

            if ($date) {
                $where[] = "DATE(created_at) = ?";
                $params[] = $date;
            }

            $whereClause = empty($where) ? '' : 'WHERE ' . implode(' AND ', $where);

            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM qiniu_sync_logs $whereClause");
            $stmt->execute($params);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            return $row['count'];
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取日志统计（按日期）
     */
    public function getLogStatsByDate($date = null)
    {
        try {
            if (!$this->db) {
                return [
                    'total' => 0,
                    'error' => 0,
                    'warning' => 0,
                    'info' => 0,
                    'debug' => 0
                ];
            }

            $where = $date ? "WHERE DATE(created_at) = ?" : "";
            $params = $date ? [$date] : [];

            $stmt = $this->db->prepare("
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN level = 'error' THEN 1 ELSE 0 END) as error,
                    SUM(CASE WHEN level = 'warning' THEN 1 ELSE 0 END) as warning,
                    SUM(CASE WHEN level = 'info' THEN 1 ELSE 0 END) as info,
                    SUM(CASE WHEN level = 'debug' THEN 1 ELSE 0 END) as debug
                FROM qiniu_sync_logs $where
            ");
            $stmt->execute($params);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            return [
                'total' => (int)$row['total'],
                'error' => (int)$row['error'],
                'warning' => (int)$row['warning'],
                'info' => (int)$row['info'],
                'debug' => (int)$row['debug']
            ];
        } catch (Exception $e) {
            return [
                'total' => 0,
                'error' => 0,
                'warning' => 0,
                'info' => 0,
                'debug' => 0
            ];
        }
    }

    /**
     * 获取可用的日志日期
     */
    public function getAvailableDates()
    {
        try {
            if (!$this->db) {
                return [date('Y-m-d')];
            }

            $stmt = $this->db->query("
                SELECT DISTINCT DATE(created_at) as log_date
                FROM qiniu_sync_logs
                ORDER BY log_date DESC
                LIMIT 30
            ");

            $dates = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $dates[] = $row['log_date'];
            }

            return empty($dates) ? [date('Y-m-d')] : $dates;
        } catch (Exception $e) {
            return [date('Y-m-d')];
        }
    }
}
