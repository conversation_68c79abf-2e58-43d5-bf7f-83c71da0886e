<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试修复结果</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <h2>测试修复结果</h2>
        
        <?php
        session_start();
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = 'admin';
        ?>
        
        <div class="alert alert-info">
            <h5>修复内容</h5>
            <ul>
                <li>✅ 修复了ajax.php的请求验证逻辑</li>
                <li>✅ 改进了JSON解析错误处理</li>
                <li>✅ 统一了JavaScript错误处理格式</li>
                <li>✅ 自动设置登录会话</li>
            </ul>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>AJAX功能测试</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button class="btn btn-primary w-100 mb-2" onclick="testValidateConfig()">测试验证配置</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-secondary w-100 mb-2" onclick="testSaveConfig()">测试保存配置</button>
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-info w-100 mb-2" onclick="testSaveDirs()">测试保存目录</button>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>测试结果:</h6>
                    <div id="test-results" style="background: #f8f9fa; padding: 15px; border-radius: 5px; min-height: 100px;">
                        点击上面的按钮开始测试...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>下一步</h5>
            </div>
            <div class="card-body">
                <p>如果上面的测试都成功，您可以：</p>
                <div class="d-flex gap-2">
                    <a href="index.php" class="btn btn-success">返回管理面板</a>
                    <a href="files.php" class="btn btn-primary">查看文件管理</a>
                    <a href="test_new_features.php" class="btn btn-info">测试新功能</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResult(message, isSuccess = false) {
            const resultDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const color = isSuccess ? 'text-success' : 'text-danger';
            resultDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function testValidateConfig() {
            showResult('开始测试验证配置...', true);
            
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'validate_config'})
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.valid) {
                    showResult('✅ 配置验证成功', true);
                } else {
                    showResult('⚠️ 配置验证失败: ' + (data.errors ? data.errors.join(', ') : '未知错误'));
                }
            })
            .catch(error => {
                showResult('❌ 验证配置请求失败: ' + error.message);
            });
        }

        function testSaveConfig() {
            showResult('开始测试保存配置...', true);
            
            const testConfig = {
                access_key: 'test_access_key',
                secret_key: 'test_secret_key',
                bucket: 'test_bucket',
                domain: 'test.domain.com',
                prefix: 'test/',
                region: 'z0',
                use_https: true,
                use_cdn_domain: false
            };
            
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'save_config', config: testConfig})
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showResult('✅ 配置保存成功', true);
                } else {
                    showResult('⚠️ 配置保存失败: ' + (data.error || data.message || '未知错误'));
                }
            })
            .catch(error => {
                showResult('❌ 保存配置请求失败: ' + error.message);
            });
        }

        function testSaveDirs() {
            showResult('开始测试保存目录配置...', true);
            
            const testData = {
                action: 'save_sync_dirs',
                sync_dirs: ['D:/xampp/htdocs/BDLX/uploads', 'D:/xampp/htdocs/BDLX/images'],
                allowed_extensions: ['jpg', 'jpeg', 'png', 'gif', 'txt'],
                max_file_size: 10,
                auto_scan: true,
                delete_after_sync: false
            };
            
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(testData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showResult('✅ 目录配置保存成功', true);
                } else {
                    showResult('⚠️ 目录配置保存失败: ' + (data.error || data.message || '未知错误'));
                }
            })
            .catch(error => {
                showResult('❌ 保存目录配置请求失败: ' + error.message);
            });
        }
    </script>
</body>
</html>
