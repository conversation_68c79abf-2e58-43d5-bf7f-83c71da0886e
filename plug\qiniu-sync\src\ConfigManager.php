<?php
/**
 * 七牛云同步插件配置管理器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class QiniuSyncConfigManager
{
    private $db;
    private $config;
    private $logger;
    private $encryptionKey;
    private $cache = [];
    
    /**
     * 构造函数
     */
    public function __construct($db, $config, $logger)
    {
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
        $this->initializeEncryption();
    }
    
    /**
     * 初始化加密
     */
    private function initializeEncryption()
    {
        $this->encryptionKey = $this->getConfig('security.encryption_key');
        if (empty($this->encryptionKey)) {
            $this->encryptionKey = bin2hex(random_bytes(32));
            $this->updateConfig('security.encryption_key', $this->encryptionKey, false);
        }
    }
    
    /**
     * 获取配置值
     */
    public function getConfig($key, $default = null)
    {
        // 先从缓存获取
        if (isset($this->cache[$key])) {
            return $this->cache[$key];
        }
        
        try {
            $stmt = $this->db->prepare("SELECT config_value, config_type, is_encrypted FROM qiniu_sync_config WHERE config_key = ?");
            $stmt->execute([$key]);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$row) {
                return $default;
            }
            
            $value = $row['config_value'];
            
            // 解密敏感配置
            if ($row['is_encrypted']) {
                $value = $this->decrypt($value);
            }
            
            // 类型转换
            $value = $this->convertType($value, $row['config_type']);
            
            // 缓存结果
            $this->cache[$key] = $value;
            
            return $value;
        } catch (Exception $e) {
            $this->logger->error('获取配置失败', ['key' => $key, 'error' => $e->getMessage()]);
            return $default;
        }
    }
    
    /**
     * 更新配置值
     */
    public function updateConfig($key, $value, $encrypt = null)
    {
        try {
            // 判断是否需要加密
            if ($encrypt === null) {
                $encrypt = $this->shouldEncrypt($key);
            }
            
            $configType = $this->getConfigType($value);
            $configValue = $this->convertToString($value, $configType);
            
            // 加密敏感配置
            if ($encrypt) {
                $configValue = $this->encrypt($configValue);
            }
            
            $stmt = $this->db->prepare("
                INSERT INTO qiniu_sync_config (config_key, config_value, config_type, is_encrypted) 
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                    config_value = VALUES(config_value),
                    config_type = VALUES(config_type),
                    is_encrypted = VALUES(is_encrypted),
                    updated_at = CURRENT_TIMESTAMP
            ");
            
            $result = $stmt->execute([$key, $configValue, $configType, $encrypt ? 1 : 0]);
            
            if ($result) {
                // 更新缓存
                $this->cache[$key] = $value;
                $this->logger->info('配置更新成功', ['key' => $key]);
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            $this->logger->error('配置更新失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 批量更新配置
     */
    public function updateConfigs($configs)
    {
        $this->db->beginTransaction();
        
        try {
            foreach ($configs as $key => $value) {
                if (!$this->updateConfig($key, $value)) {
                    throw new Exception("更新配置失败: {$key}");
                }
            }
            
            $this->db->commit();
            $this->logger->info('批量配置更新成功', ['count' => count($configs)]);
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->logger->error('批量配置更新失败', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 删除配置
     */
    public function deleteConfig($key)
    {
        try {
            $stmt = $this->db->prepare("DELETE FROM qiniu_sync_config WHERE config_key = ?");
            $result = $stmt->execute([$key]);
            
            if ($result) {
                unset($this->cache[$key]);
                $this->logger->info('配置删除成功', ['key' => $key]);
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            $this->logger->error('配置删除失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * 获取所有配置
     */
    public function getAllConfigs($includeEncrypted = false)
    {
        try {
            $sql = "SELECT config_key, config_value, config_type, is_encrypted, description FROM qiniu_sync_config";
            if (!$includeEncrypted) {
                $sql .= " WHERE is_encrypted = 0";
            }
            
            $stmt = $this->db->query($sql);
            $configs = [];
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $value = $row['config_value'];
                
                if ($row['is_encrypted'] && $includeEncrypted) {
                    $value = $this->decrypt($value);
                }
                
                $configs[$row['config_key']] = [
                    'value' => $this->convertType($value, $row['config_type']),
                    'type' => $row['config_type'],
                    'encrypted' => (bool)$row['is_encrypted'],
                    'description' => $row['description']
                ];
            }
            
            return $configs;
        } catch (Exception $e) {
            $this->logger->error('获取所有配置失败', ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 验证七牛云配置
     */
    public function validateQiniuConfig($config = null)
    {
        if ($config === null) {
            $config = [
                'access_key' => $this->getConfig('qiniu.access_key'),
                'secret_key' => $this->getConfig('qiniu.secret_key'),
                'bucket' => $this->getConfig('qiniu.bucket'),
                'domain' => $this->getConfig('qiniu.domain')
            ];
        }

        $errors = [];

        // 检查必填字段
        if (empty($config['access_key'])) {
            $errors[] = 'AccessKey不能为空';
        }

        if (empty($config['secret_key'])) {
            $errors[] = 'SecretKey不能为空';
        }

        if (empty($config['bucket'])) {
            $errors[] = 'Bucket不能为空';
        }

        if (empty($config['domain'])) {
            $errors[] = 'Domain不能为空';
        }

        if (!empty($errors)) {
            return ['valid' => false, 'errors' => $errors];
        }

        // 基本格式验证
        if (strlen($config['access_key']) < 20) {
            $errors[] = 'AccessKey格式不正确（长度不足）';
        }

        if (strlen($config['secret_key']) < 30) {
            $errors[] = 'SecretKey格式不正确（长度不足）';
        }

        if (!preg_match('/^[a-z0-9\-]+$/', $config['bucket'])) {
            $errors[] = 'Bucket名称格式不正确（只能包含小写字母、数字和连字符）';
        }

        if (!filter_var('http://' . $config['domain'], FILTER_VALIDATE_URL)) {
            $errors[] = 'Domain格式不正确';
        }

        if (!empty($errors)) {
            return ['valid' => false, 'errors' => $errors];
        }

        // 检查七牛云SDK是否可用
        if (!class_exists('Qiniu\Auth')) {
            // 尝试加载Composer自动加载器
            $autoloadPaths = [
                QINIU_SYNC_ROOT . '/vendor/autoload.php',
                dirname(QINIU_SYNC_ROOT) . '/vendor/autoload.php',
                dirname(__DIR__) . '/vendor/autoload.php'
            ];

            $autoloadFound = false;
            foreach ($autoloadPaths as $autoloadPath) {
                if (file_exists($autoloadPath)) {
                    require_once $autoloadPath;
                    $autoloadFound = true;
                    break;
                }
            }

            if (!$autoloadFound || !class_exists('Qiniu\Auth')) {
                return [
                    'valid' => false,
                    'errors' => ['七牛云SDK未安装，请先安装依赖包'],
                    'need_install_sdk' => true
                ];
            }
        }

        // 测试连接
        try {

            $auth = new \Qiniu\Auth($config['access_key'], $config['secret_key']);
            $bucketManager = new \Qiniu\Storage\BucketManager($auth);

            // 获取存储空间信息
            list($buckets, $err) = $bucketManager->buckets();

            if ($err !== null) {
                return ['valid' => false, 'errors' => ['连接失败: ' . $err->message()]];
            }

            // 检查bucket是否存在
            if (!in_array($config['bucket'], $buckets)) {
                return ['valid' => false, 'errors' => ['指定的Bucket不存在']];
            }

            // 测试上传权限
            $token = $auth->uploadToken($config['bucket']);
            if (empty($token)) {
                return ['valid' => false, 'errors' => ['无法生成上传凭证']];
            }

            return ['valid' => true, 'message' => '配置验证成功'];

        } catch (Exception $e) {
            return ['valid' => false, 'errors' => ['验证失败: ' . $e->getMessage()]];
        }
    }
    
    /**
     * 获取七牛云配置
     */
    public function getQiniuConfig()
    {
        return [
            'access_key' => $this->getConfig('qiniu.access_key'),
            'secret_key' => $this->getConfig('qiniu.secret_key'),
            'bucket' => $this->getConfig('qiniu.bucket'),
            'domain' => $this->getConfig('qiniu.domain'),
            'prefix' => $this->getConfig('qiniu.prefix', ''),
            'region' => $this->getConfig('qiniu.region', 'z0'),
            'use_https' => $this->getConfig('qiniu.use_https', true),
            'use_cdn_domain' => $this->getConfig('qiniu.use_cdn_domain', true)
        ];
    }
    
    /**
     * 更新七牛云配置
     */
    public function updateQiniuConfig($config)
    {
        $qiniuConfigs = [
            'qiniu.access_key' => $config['access_key'] ?? '',
            'qiniu.secret_key' => $config['secret_key'] ?? '',
            'qiniu.bucket' => $config['bucket'] ?? '',
            'qiniu.domain' => $config['domain'] ?? '',
            'qiniu.prefix' => $config['prefix'] ?? '',
            'qiniu.region' => $config['region'] ?? 'z0',
            'qiniu.use_https' => $config['use_https'] ?? true,
            'qiniu.use_cdn_domain' => $config['use_cdn_domain'] ?? true
        ];
        
        return $this->updateConfigs($qiniuConfigs);
    }
    
    /**
     * 判断是否需要加密
     */
    private function shouldEncrypt($key)
    {
        $encryptKeys = [
            'qiniu.access_key',
            'qiniu.secret_key',
            'security.encryption_key',
            'notification.webhook.secret'
        ];
        
        return in_array($key, $encryptKeys);
    }
    
    /**
     * 加密数据
     */
    private function encrypt($data)
    {
        if (empty($this->encryptionKey)) {
            return $data;
        }
        
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $this->encryptionKey, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * 解密数据
     */
    private function decrypt($data)
    {
        if (empty($this->encryptionKey)) {
            return $data;
        }
        
        $data = base64_decode($data);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', $this->encryptionKey, 0, $iv);
    }
    
    /**
     * 获取配置类型
     */
    private function getConfigType($value)
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }
    
    /**
     * 类型转换
     */
    private function convertType($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int)$value;
            case 'float':
                return (float)$value;
            case 'json':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
    
    /**
     * 转换为字符串
     */
    private function convertToString($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return $value ? 'true' : 'false';
            case 'json':
                return json_encode($value, JSON_UNESCAPED_UNICODE);
            default:
                return (string)$value;
        }
    }
    
    /**
     * 清除缓存
     */
    public function clearCache()
    {
        $this->cache = [];
    }
    
    /**
     * 导出配置
     */
    public function exportConfig($includeEncrypted = false)
    {
        $configs = $this->getAllConfigs($includeEncrypted);
        
        $export = [
            'version' => QINIU_SYNC_VERSION,
            'exported_at' => date('Y-m-d H:i:s'),
            'configs' => $configs
        ];
        
        return json_encode($export, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * 导入配置
     */
    public function importConfig($configJson)
    {
        try {
            $data = json_decode($configJson, true);

            if (!$data || !isset($data['configs'])) {
                throw new Exception('配置格式无效');
            }

            $configs = [];
            foreach ($data['configs'] as $key => $config) {
                $configs[$key] = $config['value'];
            }

            return $this->updateConfigs($configs);
        } catch (Exception $e) {
            $this->logger->error('导入配置失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取数据库连接
     */
    public function getDatabase()
    {
        return $this->db;
    }
}
