<?php
/**
 * 七牛云同步插件调试脚本
 */

echo "开始调试...\n";

// 1. 测试bootstrap
echo "1. 加载bootstrap...\n";
try {
    require_once __DIR__ . '/bootstrap.php';
    echo "   ✓ bootstrap加载成功\n";
} catch (Exception $e) {
    echo "   ❌ bootstrap加载失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. 测试基本类
echo "2. 测试基本类...\n";
try {
    echo "   - 测试QiniuSyncLogger...\n";
    $logger = new QiniuSyncLogger();
    echo "   ✓ QiniuSyncLogger创建成功\n";
    
    echo "   - 测试QiniuSync主类...\n";
    $qiniuSync = new QiniuSync();
    echo "   ✓ QiniuSync创建成功\n";
    
} catch (Exception $e) {
    echo "   ❌ 类创建失败: " . $e->getMessage() . "\n";
    echo "   错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}

echo "调试完成，基本功能正常\n";
?>
