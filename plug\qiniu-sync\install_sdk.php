<?php
/**
 * 七牛云SDK安装脚本
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置执行时间限制
set_time_limit(300); // 5分钟

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'install_sdk':
                $result = installQiniuSDK();
                break;
            case 'check_composer':
                $result = checkComposer();
                break;
            default:
                throw new Exception('未知操作');
        }
        
        echo json_encode($result);
        exit;
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}

/**
 * 检查Composer是否可用
 */
function checkComposer() {
    $composerPaths = [
        'composer',
        'composer.phar',
        '/usr/local/bin/composer',
        '/usr/bin/composer'
    ];
    
    foreach ($composerPaths as $path) {
        $output = [];
        $returnVar = 0;
        exec("$path --version 2>&1", $output, $returnVar);
        
        if ($returnVar === 0) {
            return [
                'success' => true,
                'composer_path' => $path,
                'version' => implode(' ', $output)
            ];
        }
    }
    
    return [
        'success' => false,
        'error' => 'Composer未找到，请先安装Composer'
    ];
}

/**
 * 安装七牛云SDK
 */
function installQiniuSDK() {
    $pluginDir = __DIR__;
    
    // 检查Composer
    $composerCheck = checkComposer();
    if (!$composerCheck['success']) {
        return $composerCheck;
    }
    
    $composerPath = $composerCheck['composer_path'];
    
    // 创建composer.json
    $composerJson = [
        'name' => 'bdlx/qiniu-sync-plugin',
        'description' => '七牛云同步插件',
        'require' => [
            'qiniu/php-sdk' => '^7.0'
        ],
        'config' => [
            'vendor-dir' => 'vendor'
        ]
    ];
    
    $composerJsonPath = $pluginDir . '/composer.json';
    if (!file_put_contents($composerJsonPath, json_encode($composerJson, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))) {
        return ['success' => false, 'error' => '无法创建composer.json文件'];
    }
    
    // 执行composer install
    $output = [];
    $returnVar = 0;
    
    $command = "cd \"$pluginDir\" && $composerPath install --no-dev --optimize-autoloader 2>&1";
    exec($command, $output, $returnVar);
    
    $outputText = implode("\n", $output);
    
    if ($returnVar === 0) {
        // 检查vendor/autoload.php是否存在
        if (file_exists($pluginDir . '/vendor/autoload.php')) {
            return [
                'success' => true,
                'message' => '七牛云SDK安装成功',
                'output' => $outputText
            ];
        } else {
            return [
                'success' => false,
                'error' => 'SDK安装失败：autoload.php文件未生成',
                'output' => $outputText
            ];
        }
    } else {
        return [
            'success' => false,
            'error' => 'Composer安装失败',
            'output' => $outputText
        ];
    }
}

// 检查当前状态
$sdkInstalled = file_exists(__DIR__ . '/vendor/autoload.php');
$composerExists = checkComposer()['success'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>七牛云SDK安装</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cloud-download"></i> 七牛云SDK安装
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- 状态检查 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-speedometer2"></i> 环境检查
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p>
                                    <span class="badge <?php echo $composerExists ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo $composerExists ? '✓' : '✗'; ?>
                                    </span>
                                    <strong>Composer:</strong> <?php echo $composerExists ? '已安装' : '未安装'; ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p>
                                    <span class="badge <?php echo $sdkInstalled ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo $sdkInstalled ? '✓' : '✗'; ?>
                                    </span>
                                    <strong>七牛云SDK:</strong> <?php echo $sdkInstalled ? '已安装' : '未安装'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安装操作 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-download"></i> SDK安装
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!$composerExists): ?>
                            <div class="alert alert-danger">
                                <h6><i class="bi bi-exclamation-triangle"></i> Composer未安装</h6>
                                <p>请先安装Composer，然后刷新此页面。</p>
                                <a href="https://getcomposer.org/download/" target="_blank" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-up-right"></i> 下载Composer
                                </a>
                            </div>
                        <?php elseif ($sdkInstalled): ?>
                            <div class="alert alert-success">
                                <h6><i class="bi bi-check-circle"></i> SDK已安装</h6>
                                <p>七牛云SDK已成功安装，现在可以返回配置页面。</p>
                                <a href="admin/index.php" class="btn btn-primary">
                                    <i class="bi bi-arrow-left"></i> 返回管理面板
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle"></i> 准备安装SDK</h6>
                                <p>点击下面的按钮安装七牛云PHP SDK。</p>
                            </div>
                            
                            <button id="installBtn" class="btn btn-success">
                                <i class="bi bi-download"></i> 安装七牛云SDK
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 安装日志 -->
                <div id="logCard" class="card" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-terminal"></i> 安装日志
                        </h5>
                    </div>
                    <div class="card-body">
                        <pre id="logOutput" class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 mb-0">正在安装SDK，请稍候...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        
        document.getElementById('installBtn')?.addEventListener('click', function() {
            installSDK();
        });
        
        function installSDK() {
            loadingModal.show();
            
            const formData = new FormData();
            formData.append('action', 'install_sdk');
            
            fetch('install_sdk.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                loadingModal.hide();
                
                const logCard = document.getElementById('logCard');
                const logOutput = document.getElementById('logOutput');
                
                logCard.style.display = 'block';
                logOutput.textContent = result.output || result.error || '无输出';
                
                if (result.success) {
                    alert('SDK安装成功！页面将自动刷新。');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    alert('SDK安装失败：' + result.error);
                }
            })
            .catch(error => {
                loadingModal.hide();
                alert('请求失败：' + error.message);
            });
        }
    </script>
</body>
</html>
