{"datetime":"2025-08-27 21:47:35","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0ac57602"}}
{"datetime":"2025-08-27 21:47:35","level":"CRITICAL","message":"未捕获的异常: Undefined constant \"EXCEL_DIR\"","context":{"message":"Undefined constant \"EXCEL_DIR\"","file":"D:\\xampp\\htdocs\\BDLX\\admin\\config.php","line":44,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(136): require_once()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0ac57602"}}
{"datetime":"2025-08-27 21:47:35","level":"INFO","message":"应用结束","context":{"execution_time":0.0048,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0ac57602"}}
{"datetime":"2025-08-27 21:47:36","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"acd34012"}}
{"datetime":"2025-08-27 21:47:36","level":"CRITICAL","message":"未捕获的异常: Undefined constant \"EXCEL_DIR\"","context":{"message":"Undefined constant \"EXCEL_DIR\"","file":"D:\\xampp\\htdocs\\BDLX\\admin\\config.php","line":44,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(136): require_once()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"acd34012"}}
{"datetime":"2025-08-27 21:47:36","level":"INFO","message":"应用结束","context":{"execution_time":0.0036,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"acd34012"}}
{"datetime":"2025-08-27 21:47:41","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2099ba2b"}}
{"datetime":"2025-08-27 21:47:41","level":"CRITICAL","message":"未捕获的异常: Undefined constant \"EXCEL_DIR\"","context":{"message":"Undefined constant \"EXCEL_DIR\"","file":"D:\\xampp\\htdocs\\BDLX\\admin\\config.php","line":44,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(136): require_once()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2099ba2b"}}
{"datetime":"2025-08-27 21:47:41","level":"INFO","message":"应用结束","context":{"execution_time":0.0138,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2099ba2b"}}
{"datetime":"2025-08-27 21:48:10","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c5924c9f"}}
{"datetime":"2025-08-27 21:48:10","level":"CRITICAL","message":"未捕获的异常: Undefined constant \"EXCEL_DIR\"","context":{"message":"Undefined constant \"EXCEL_DIR\"","file":"D:\\xampp\\htdocs\\BDLX\\admin\\config.php","line":44,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(136): require_once()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c5924c9f"}}
{"datetime":"2025-08-27 21:48:10","level":"INFO","message":"应用结束","context":{"execution_time":0.0045,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c5924c9f"}}
{"datetime":"2025-08-27 21:53:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4451a6c9"}}
{"datetime":"2025-08-27 21:53:44","level":"INFO","message":"应用结束","context":{"execution_time":0.0092,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4451a6c9"}}
{"datetime":"2025-08-27 21:53:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"afae6d6e"}}
{"datetime":"2025-08-27 21:53:44","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"afae6d6e"}}
{"datetime":"2025-08-27 21:53:44","level":"INFO","message":"应用结束","context":{"execution_time":0.0193,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"afae6d6e"}}
{"datetime":"2025-08-27 21:53:48","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4e5ad300"}}
{"datetime":"2025-08-27 21:53:48","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4e5ad300"}}
{"datetime":"2025-08-27 21:53:48","level":"INFO","message":"应用结束","context":{"execution_time":0.0113,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4e5ad300"}}
{"datetime":"2025-08-27 21:53:48","level":"INFO","message":"应用启动","context":{"memory_usage":4194304,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"43b38d64"}}
{"datetime":"2025-08-27 21:53:48","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"43b38d64"}}
{"datetime":"2025-08-27 21:53:48","level":"INFO","message":"应用结束","context":{"execution_time":0.0098,"memory_peak":4194304,"memory_end":4194304},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"43b38d64"}}
{"datetime":"2025-08-27 21:53:50","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"73d6b24d"}}
{"datetime":"2025-08-27 21:53:50","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"73d6b24d"}}
{"datetime":"2025-08-27 21:53:50","level":"INFO","message":"应用结束","context":{"execution_time":0.0065,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"73d6b24d"}}
{"datetime":"2025-08-27 21:53:59","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f60715c6"}}
{"datetime":"2025-08-27 21:53:59","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f60715c6"}}
{"datetime":"2025-08-27 21:53:59","level":"INFO","message":"应用结束","context":{"execution_time":0.0096,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f60715c6"}}
{"datetime":"2025-08-27 21:54:48","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"defa667f"}}
{"datetime":"2025-08-27 21:54:48","level":"INFO","message":"应用结束","context":{"execution_time":0.0086,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"defa667f"}}
{"datetime":"2025-08-27 21:54:51","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0e15577a"}}
{"datetime":"2025-08-27 21:54:51","level":"INFO","message":"应用结束","context":{"execution_time":0.0037,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0e15577a"}}
{"datetime":"2025-08-27 21:54:51","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"dbae1e92"}}
{"datetime":"2025-08-27 21:54:51","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"dbae1e92"}}
{"datetime":"2025-08-27 21:54:51","level":"INFO","message":"应用结束","context":{"execution_time":0.0164,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"dbae1e92"}}
{"datetime":"2025-08-27 21:54:57","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7562a132"}}
{"datetime":"2025-08-27 21:54:57","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7562a132"}}
{"datetime":"2025-08-27 21:54:57","level":"INFO","message":"应用结束","context":{"execution_time":0.0163,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7562a132"}}
{"datetime":"2025-08-27 21:57:48","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c929aa32"}}
{"datetime":"2025-08-27 21:57:48","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c929aa32"}}
{"datetime":"2025-08-27 21:57:48","level":"INFO","message":"应用结束","context":{"execution_time":0.0204,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c929aa32"}}
{"datetime":"2025-08-27 21:57:49","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0e45fe92"}}
{"datetime":"2025-08-27 21:57:49","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0e45fe92"}}
{"datetime":"2025-08-27 21:57:49","level":"INFO","message":"应用结束","context":{"execution_time":0.0068,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0e45fe92"}}
{"datetime":"2025-08-27 21:57:49","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d6f6e9f6"}}
{"datetime":"2025-08-27 21:57:49","level":"CRITICAL","message":"未捕获的异常: Call to undefined method SafeDatabase::escape()","context":{"message":"Call to undefined method SafeDatabase::escape()","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":73,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(170): GameTypeHelper::getGameTypeByName('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(165): GameTypeHelper::getGameTypeCssClass('\\xE6\\xB5\\x81\\xE5\\x89\\x91\\xE9\\x81\\x93')\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d6f6e9f6"}}
{"datetime":"2025-08-27 21:57:49","level":"INFO","message":"应用结束","context":{"execution_time":0.0073,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d6f6e9f6"}}
{"datetime":"2025-08-27 22:02:40","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"5e1aa48e"}}
{"datetime":"2025-08-27 22:02:40","level":"INFO","message":"应用结束","context":{"execution_time":0.0356,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"5e1aa48e"}}
{"datetime":"2025-08-27 22:02:40","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7b261432"}}
{"datetime":"2025-08-27 22:02:40","level":"INFO","message":"应用结束","context":{"execution_time":0.0127,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7b261432"}}
{"datetime":"2025-08-27 22:02:41","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"05a42ddc"}}
{"datetime":"2025-08-27 22:02:41","level":"INFO","message":"应用结束","context":{"execution_time":0.0213,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"05a42ddc"}}
{"datetime":"2025-08-27 22:02:41","level":"INFO","message":"应用启动","context":{"memory_usage":4194304,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"d7f847ec"}}
{"datetime":"2025-08-27 22:02:41","level":"INFO","message":"应用结束","context":{"execution_time":0.0122,"memory_peak":4194304,"memory_end":4194304},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"d7f847ec"}}
{"datetime":"2025-08-27 22:02:41","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fb4d9236"}}
{"datetime":"2025-08-27 22:02:41","level":"INFO","message":"应用结束","context":{"execution_time":0.0127,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fb4d9236"}}
{"datetime":"2025-08-27 22:02:41","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"587d9779"}}
{"datetime":"2025-08-27 22:02:41","level":"INFO","message":"应用结束","context":{"execution_time":0.0144,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"587d9779"}}
{"datetime":"2025-08-27 22:02:45","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8c9b60b3"}}
{"datetime":"2025-08-27 22:02:45","level":"INFO","message":"应用结束","context":{"execution_time":0.0143,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8c9b60b3"}}
{"datetime":"2025-08-27 22:02:47","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9485b616"}}
{"datetime":"2025-08-27 22:02:47","level":"INFO","message":"应用结束","context":{"execution_time":0.013,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9485b616"}}
{"datetime":"2025-08-27 22:03:22","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"92467f1b"}}
{"datetime":"2025-08-27 22:03:22","level":"INFO","message":"应用结束","context":{"execution_time":0.0241,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"92467f1b"}}
{"datetime":"2025-08-27 22:08:08","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"39d5b385"}}
{"datetime":"2025-08-27 22:08:08","level":"INFO","message":"应用结束","context":{"execution_time":0.0324,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"39d5b385"}}
{"datetime":"2025-08-27 22:17:58","level":"CRITICAL","message":"Uncaught exception: Class \"ConfigServiceProvider\" not found","context":{"message":"Class \"ConfigServiceProvider\" not found","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php","line":276,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(21): require_once('D:\\\\xampp\\\\htdocs...')\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#3 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8fc2dea5"}}
{"datetime":"2025-08-27 22:18:00","level":"CRITICAL","message":"Uncaught exception: Class \"ConfigServiceProvider\" not found","context":{"message":"Class \"ConfigServiceProvider\" not found","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php","line":276,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(21): require_once('D:\\\\xampp\\\\htdocs...')\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#3 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"203f6169"}}
{"datetime":"2025-08-27 22:18:06","level":"CRITICAL","message":"Uncaught exception: Class \"ConfigServiceProvider\" not found","context":{"message":"Class \"ConfigServiceProvider\" not found","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php","line":276,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(21): require_once('D:\\\\xampp\\\\htdocs...')\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#3 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"11274dcb"}}
{"datetime":"2025-08-27 22:18:09","level":"CRITICAL","message":"Uncaught exception: Class \"ConfigServiceProvider\" not found","context":{"message":"Class \"ConfigServiceProvider\" not found","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php","line":276,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(21): require_once('D:\\\\xampp\\\\htdocs...')\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#3 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"65346827"}}
{"datetime":"2025-08-27 22:18:09","level":"CRITICAL","message":"Uncaught exception: Class \"ConfigServiceProvider\" not found","context":{"message":"Class \"ConfigServiceProvider\" not found","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php","line":276,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(21): require_once('D:\\\\xampp\\\\htdocs...')\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#3 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"31696c29"}}
{"datetime":"2025-08-27 22:18:17","level":"CRITICAL","message":"Uncaught exception: Class \"ConfigServiceProvider\" not found","context":{"message":"Class \"ConfigServiceProvider\" not found","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php","line":276,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(21): require_once('D:\\\\xampp\\\\htdocs...')\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#3 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee3359da"}}
{"datetime":"2025-08-27 22:18:20","level":"CRITICAL","message":"Uncaught exception: Class \"ConfigServiceProvider\" not found","context":{"message":"Class \"ConfigServiceProvider\" not found","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php","line":276,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(21): require_once('D:\\\\xampp\\\\htdocs...')\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#3 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"94be6927"}}
{"datetime":"2025-08-27 22:21:16","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"27929c80"}}
{"datetime":"2025-08-27 22:21:17","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9f7a5bf6"}}
{"datetime":"2025-08-27 22:21:18","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7a7a70b3"}}
{"datetime":"2025-08-27 22:21:18","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"24786fca"}}
{"datetime":"2025-08-27 22:21:20","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"262863e4"}}
{"datetime":"2025-08-27 22:21:20","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"336462d1"}}
{"datetime":"2025-08-27 22:23:12","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3dd4d194"}}
{"datetime":"2025-08-27 22:26:36","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"401f35e1"}}
{"datetime":"2025-08-27 22:26:36","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"56001432"}}
{"datetime":"2025-08-27 22:26:36","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"358aa61c"}}
{"datetime":"2025-08-27 22:26:37","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c601aacc"}}
{"datetime":"2025-08-27 22:27:00","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c507aed0"}}
{"datetime":"2025-08-27 22:27:05","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2c766cac"}}
{"datetime":"2025-08-27 22:27:17","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"dd5994d8"}}
{"datetime":"2025-08-27 22:32:37","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"331a6bdf"}}
{"datetime":"2025-08-27 22:33:17","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e137041e"}}
{"datetime":"2025-08-27 22:33:17","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"95f314ff"}}
{"datetime":"2025-08-27 22:33:18","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"60e63c84"}}
{"datetime":"2025-08-27 22:33:19","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"13983203"}}
{"datetime":"2025-08-27 22:33:19","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"59aba9ab"}}
{"datetime":"2025-08-27 22:33:22","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"08a5ba56"}}
{"datetime":"2025-08-27 22:33:22","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9df9b0cb"}}
{"datetime":"2025-08-27 22:33:22","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"08b0c2bf"}}
{"datetime":"2025-08-27 22:33:41","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c14a40bc"}}
{"datetime":"2025-08-27 22:35:58","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\test_bootstrap.php(6): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d991047e"}}
{"datetime":"2025-08-27 22:42:55","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(294): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(24): require_once('D:\\\\xampp\\\\htdocs...')\n#4 D:\\xampp\\htdocs\\BDLX\\admin\\test_login.php(26): require_once('D:\\\\xampp\\\\htdocs...')\n#5 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b5313c30"}}
{"datetime":"2025-08-27 22:43:38","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d320cfd8"}}
{"datetime":"2025-08-27 22:43:38","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d320cfd8"}}
{"datetime":"2025-08-27 22:43:51","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6edb9efa"}}
{"datetime":"2025-08-27 22:43:51","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6edb9efa"}}
{"datetime":"2025-08-27 22:43:52","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4a6a1c71"}}
{"datetime":"2025-08-27 22:43:52","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\login.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4a6a1c71"}}
{"datetime":"2025-08-27 22:44:13","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1c30fccc"}}
{"datetime":"2025-08-27 22:44:13","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\setup_database.php(10): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1c30fccc"}}
{"datetime":"2025-08-27 22:44:48","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee9efda3"}}
{"datetime":"2025-08-27 22:44:48","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee9efda3"}}
{"datetime":"2025-08-27 22:44:54","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"449868ca"}}
{"datetime":"2025-08-27 22:44:54","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"449868ca"}}
{"datetime":"2025-08-27 22:44:54","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"19427780"}}
{"datetime":"2025-08-27 22:44:54","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"19427780"}}
{"datetime":"2025-08-27 22:45:20","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"65c673eb"}}
{"datetime":"2025-08-27 22:45:20","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(3): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"65c673eb"}}
{"datetime":"2025-08-27 22:46:18","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8d34fdc6"}}
{"datetime":"2025-08-27 22:46:18","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\db_test.php(52): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8d34fdc6"}}
{"datetime":"2025-08-27 22:46:40","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fcde300d"}}
{"datetime":"2025-08-27 22:46:40","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fcde300d"}}
{"datetime":"2025-08-27 22:46:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"536a9c26"}}
{"datetime":"2025-08-27 22:46:44","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"536a9c26"}}
{"datetime":"2025-08-27 22:46:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"91f649b7"}}
{"datetime":"2025-08-27 22:46:44","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"91f649b7"}}
{"datetime":"2025-08-27 22:46:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8820b619"}}
{"datetime":"2025-08-27 22:46:44","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8820b619"}}
{"datetime":"2025-08-27 22:46:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ebd88841"}}
{"datetime":"2025-08-27 22:46:44","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ebd88841"}}
{"datetime":"2025-08-27 22:46:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"da2f9583"}}
{"datetime":"2025-08-27 22:46:44","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"da2f9583"}}
{"datetime":"2025-08-27 22:47:57","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"90101e09"}}
{"datetime":"2025-08-27 22:47:57","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"90101e09"}}
{"datetime":"2025-08-27 22:48:39","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"a2d0163d"}}
{"datetime":"2025-08-27 22:48:39","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"a2d0163d"}}
{"datetime":"2025-08-27 22:48:40","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"400e780b"}}
{"datetime":"2025-08-27 22:48:40","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"400e780b"}}
{"datetime":"2025-08-27 22:49:23","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2c18fc22"}}
{"datetime":"2025-08-27 22:49:23","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2c18fc22"}}
{"datetime":"2025-08-27 22:49:24","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bf921ed2"}}
{"datetime":"2025-08-27 22:49:24","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\index.php(14): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bf921ed2"}}
{"datetime":"2025-08-27 22:49:27","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e4ce7d12"}}
{"datetime":"2025-08-27 22:49:27","level":"CRITICAL","message":"Uncaught exception: Call to a member function singleton() on string","context":{"message":"Call to a member function singleton() on string","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\ServiceProvider.php","line":121,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(253): ConfigServiceProvider->register()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\Container.php(276): Container::registerProvider(Object(ConfigServiceProvider))\n#2 D:\\xampp\\htdocs\\BDLX\\admin\\bootstrap.php(62): Container::bootstrap()\n#3 D:\\xampp\\htdocs\\BDLX\\admin\\setup_database.php(10): require_once('D:\\\\xampp\\\\htdocs...')\n#4 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e4ce7d12"}}
{"datetime":"2025-08-27 22:50:37","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c0b87f7c"}}
{"datetime":"2025-08-27 22:50:37","level":"INFO","message":"应用结束","context":{"execution_time":0.0301,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c0b87f7c"}}
{"datetime":"2025-08-27 22:50:45","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"5c28cda3"}}
{"datetime":"2025-08-27 22:50:45","level":"INFO","message":"应用结束","context":{"execution_time":0.0242,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"5c28cda3"}}
{"datetime":"2025-08-27 22:50:51","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fcf7717d"}}
{"datetime":"2025-08-27 22:50:51","level":"INFO","message":"应用结束","context":{"execution_time":0.015,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fcf7717d"}}
{"datetime":"2025-08-27 22:50:51","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9585bc34"}}
{"datetime":"2025-08-27 22:50:51","level":"INFO","message":"应用结束","context":{"execution_time":0.0265,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9585bc34"}}
{"datetime":"2025-08-27 22:50:57","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ab574b35"}}
{"datetime":"2025-08-27 22:50:57","level":"INFO","message":"应用结束","context":{"execution_time":0.0039,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ab574b35"}}
{"datetime":"2025-08-27 22:51:03","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d0fab684"}}
{"datetime":"2025-08-27 22:51:03","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":1,"ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d0fab684"}}
{"datetime":"2025-08-27 22:51:03","level":"INFO","message":"应用结束","context":{"execution_time":0.0593,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d0fab684"}}
{"datetime":"2025-08-27 22:51:03","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2228948c"}}
{"datetime":"2025-08-27 22:51:03","level":"INFO","message":"应用结束","context":{"execution_time":0.0123,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2228948c"}}
{"datetime":"2025-08-27 22:51:05","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4f0c5432"}}
{"datetime":"2025-08-27 22:51:05","level":"INFO","message":"应用结束","context":{"execution_time":0.0136,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4f0c5432"}}
{"datetime":"2025-08-27 22:52:58","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"a7600a96"}}
{"datetime":"2025-08-27 22:52:58","level":"INFO","message":"应用结束","context":{"execution_time":0.0205,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"a7600a96"}}
{"datetime":"2025-08-27 23:09:40","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1a69b227"}}
{"datetime":"2025-08-27 23:09:40","level":"INFO","message":"应用结束","context":{"execution_time":0.0207,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1a69b227"}}
{"datetime":"2025-08-27 23:09:40","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ef312b7c"}}
{"datetime":"2025-08-27 23:09:40","level":"INFO","message":"应用结束","context":{"execution_time":0.0275,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ef312b7c"}}
{"datetime":"2025-08-27 23:09:41","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"652eda9b"}}
{"datetime":"2025-08-27 23:09:41","level":"INFO","message":"应用结束","context":{"execution_time":0.0135,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"652eda9b"}}
{"datetime":"2025-08-27 23:09:52","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c041c905"}}
{"datetime":"2025-08-27 23:09:52","level":"INFO","message":"应用结束","context":{"execution_time":0.0234,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c041c905"}}
{"datetime":"2025-08-27 23:09:53","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9a484e2e"}}
{"datetime":"2025-08-27 23:09:53","level":"INFO","message":"应用结束","context":{"execution_time":0.0191,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9a484e2e"}}
{"datetime":"2025-08-27 23:10:01","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"65d49799"}}
{"datetime":"2025-08-27 23:10:02","level":"INFO","message":"应用结束","context":{"execution_time":0.0147,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"65d49799"}}
{"datetime":"2025-08-27 23:11:02","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"70faba68"}}
{"datetime":"2025-08-27 23:11:02","level":"INFO","message":"应用结束","context":{"execution_time":0.0177,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"70faba68"}}
{"datetime":"2025-08-27 23:11:02","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7648cba4"}}
{"datetime":"2025-08-27 23:11:02","level":"INFO","message":"应用结束","context":{"execution_time":0.0222,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7648cba4"}}
{"datetime":"2025-08-27 23:11:02","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c56332ae"}}
{"datetime":"2025-08-27 23:11:02","level":"INFO","message":"应用结束","context":{"execution_time":0.0181,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c56332ae"}}
{"datetime":"2025-08-27 23:11:07","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"29856cb2"}}
{"datetime":"2025-08-27 23:11:07","level":"INFO","message":"应用结束","context":{"execution_time":0.0159,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"29856cb2"}}
{"datetime":"2025-08-28 00:01:48","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b3002fee"}}
{"datetime":"2025-08-28 00:01:48","level":"INFO","message":"应用结束","context":{"execution_time":0.0152,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b3002fee"}}
{"datetime":"2025-08-29 01:00:39","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7dd38d6b"}}
{"datetime":"2025-08-29 01:00:39","level":"INFO","message":"应用结束","context":{"execution_time":0.0065,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7dd38d6b"}}
{"datetime":"2025-08-29 01:00:39","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee26762f"}}
{"datetime":"2025-08-29 01:00:39","level":"INFO","message":"应用结束","context":{"execution_time":0.0149,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee26762f"}}
{"datetime":"2025-08-29 01:00:45","level":"INFO","message":"应用启动","context":{"memory_usage":8388608,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"31c8c089"}}
{"datetime":"2025-08-29 01:00:45","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":1,"ip":"::1"},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"31c8c089"}}
{"datetime":"2025-08-29 01:00:45","level":"INFO","message":"应用结束","context":{"execution_time":0.0718,"memory_peak":8388608,"memory_end":8388608},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"31c8c089"}}
{"datetime":"2025-08-29 01:00:45","level":"INFO","message":"应用启动","context":{"memory_usage":4194304,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"804190c8"}}
{"datetime":"2025-08-29 01:00:45","level":"INFO","message":"应用结束","context":{"execution_time":0.0276,"memory_peak":4194304,"memory_end":4194304},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"804190c8"}}
{"datetime":"2025-08-29 01:05:14","level":"INFO","message":"应用启动","context":{"memory_usage":6291456,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"ced6dd1d"}}
{"datetime":"2025-08-29 01:05:14","level":"INFO","message":"应用结束","context":{"execution_time":0.015,"memory_peak":6291456,"memory_end":6291456},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"ced6dd1d"}}
{"datetime":"2025-08-29 01:05:16","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6f8655bc"}}
{"datetime":"2025-08-29 01:05:16","level":"INFO","message":"应用结束","context":{"execution_time":0.0236,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6f8655bc"}}
{"datetime":"2025-08-29 01:20:56","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c1f44d10"}}
{"datetime":"2025-08-29 01:20:56","level":"INFO","message":"应用结束","context":{"execution_time":0.0153,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c1f44d10"}}
{"datetime":"2025-08-29 01:21:03","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f08491cc"}}
{"datetime":"2025-08-29 01:21:03","level":"INFO","message":"应用结束","context":{"execution_time":0.0151,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f08491cc"}}
{"datetime":"2025-08-29 01:21:09","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"934bacae"}}
{"datetime":"2025-08-29 01:21:09","level":"INFO","message":"应用结束","context":{"execution_time":0.0127,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"934bacae"}}
{"datetime":"2025-08-29 01:31:00","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e1044072"}}
{"datetime":"2025-08-29 01:31:00","level":"INFO","message":"应用结束","context":{"execution_time":0.0161,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e1044072"}}
{"datetime":"2025-08-29 01:31:21","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"91121098"}}
{"datetime":"2025-08-29 01:31:21","level":"INFO","message":"应用结束","context":{"execution_time":0.0163,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"91121098"}}
{"datetime":"2025-08-29 01:31:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"330477b4"}}
{"datetime":"2025-08-29 01:31:44","level":"INFO","message":"应用结束","context":{"execution_time":0.0131,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"330477b4"}}
{"datetime":"2025-08-29 01:53:51","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8190d538"}}
{"datetime":"2025-08-29 01:53:51","level":"INFO","message":"应用结束","context":{"execution_time":0.011,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8190d538"}}
{"datetime":"2025-08-29 01:53:51","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b2a44e71"}}
{"datetime":"2025-08-29 01:53:51","level":"INFO","message":"应用结束","context":{"execution_time":0.0088,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b2a44e71"}}
{"datetime":"2025-08-29 01:53:56","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6cfe7c1c"}}
{"datetime":"2025-08-29 01:53:56","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":1,"ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6cfe7c1c"}}
{"datetime":"2025-08-29 01:53:56","level":"INFO","message":"应用结束","context":{"execution_time":0.0613,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6cfe7c1c"}}
{"datetime":"2025-08-29 01:53:56","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d40b6a8a"}}
{"datetime":"2025-08-29 01:53:57","level":"INFO","message":"应用结束","context":{"execution_time":0.0227,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d40b6a8a"}}
{"datetime":"2025-08-29 21:24:48","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"898e5d70"}}
{"datetime":"2025-08-29 21:24:48","level":"INFO","message":"应用结束","context":{"execution_time":0.0392,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"898e5d70"}}
{"datetime":"2025-08-29 21:24:48","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"dfd537e8"}}
{"datetime":"2025-08-29 21:24:48","level":"INFO","message":"应用结束","context":{"execution_time":0.0055,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"dfd537e8"}}
{"datetime":"2025-08-29 21:24:54","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8d488d0f"}}
{"datetime":"2025-08-29 21:24:55","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":1,"ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8d488d0f"}}
{"datetime":"2025-08-29 21:24:55","level":"INFO","message":"应用结束","context":{"execution_time":0.0972,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8d488d0f"}}
{"datetime":"2025-08-29 21:24:55","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bf8e6f3e"}}
{"datetime":"2025-08-29 21:24:55","level":"INFO","message":"应用结束","context":{"execution_time":0.0393,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bf8e6f3e"}}
{"datetime":"2025-08-29 21:25:10","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"cf694560"}}
{"datetime":"2025-08-29 21:25:10","level":"INFO","message":"应用结束","context":{"execution_time":0.0392,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"cf694560"}}
{"datetime":"2025-08-30 02:54:46","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d208f6b3"}}
{"datetime":"2025-08-30 02:54:46","level":"INFO","message":"应用结束","context":{"execution_time":0.0167,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d208f6b3"}}
{"datetime":"2025-08-30 02:54:46","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0e3ad837"}}
{"datetime":"2025-08-30 02:54:46","level":"INFO","message":"应用结束","context":{"execution_time":0.0156,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0e3ad837"}}
{"datetime":"2025-08-30 02:54:52","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0a7b2af1"}}
{"datetime":"2025-08-30 02:54:52","level":"INFO","message":"应用结束","context":{"execution_time":0.0163,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0a7b2af1"}}
{"datetime":"2025-08-30 02:54:52","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7c355558"}}
{"datetime":"2025-08-30 02:54:52","level":"INFO","message":"应用结束","context":{"execution_time":0.0055,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"7c355558"}}
{"datetime":"2025-09-01 21:45:00","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2a5d45bf"}}
{"datetime":"2025-09-01 21:45:00","level":"INFO","message":"应用结束","context":{"execution_time":0.0322,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2a5d45bf"}}
{"datetime":"2025-09-01 21:45:00","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2a71474b"}}
{"datetime":"2025-09-01 21:45:00","level":"INFO","message":"应用结束","context":{"execution_time":0.0077,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2a71474b"}}
{"datetime":"2025-09-01 21:45:07","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"57310444"}}
{"datetime":"2025-09-01 21:45:08","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":1,"ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"57310444"}}
{"datetime":"2025-09-01 21:45:08","level":"INFO","message":"应用结束","context":{"execution_time":0.1058,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"57310444"}}
{"datetime":"2025-09-01 21:45:08","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"deb545d5"}}
{"datetime":"2025-09-01 21:45:08","level":"INFO","message":"应用结束","context":{"execution_time":0.0543,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"deb545d5"}}
{"datetime":"2025-09-01 21:50:22","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8b7cc0e0"}}
{"datetime":"2025-09-01 21:50:22","level":"INFO","message":"应用结束","context":{"execution_time":0.0461,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8b7cc0e0"}}
{"datetime":"2025-09-01 21:52:19","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee285ba8"}}
{"datetime":"2025-09-01 21:52:19","level":"INFO","message":"应用结束","context":{"execution_time":0.0334,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee285ba8"}}
{"datetime":"2025-09-01 21:52:19","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3ca930c0"}}
{"datetime":"2025-09-01 21:52:19","level":"INFO","message":"应用结束","context":{"execution_time":0.0254,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3ca930c0"}}
{"datetime":"2025-09-01 21:52:19","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"502095b9"}}
{"datetime":"2025-09-01 21:52:19","level":"INFO","message":"应用结束","context":{"execution_time":0.0361,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"502095b9"}}
{"datetime":"2025-09-02 19:35:55","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1ec8c53f"}}
{"datetime":"2025-09-02 19:35:55","level":"INFO","message":"应用结束","context":{"execution_time":0.0265,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1ec8c53f"}}
{"datetime":"2025-09-02 19:35:55","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8db64cfd"}}
{"datetime":"2025-09-02 19:35:55","level":"INFO","message":"应用结束","context":{"execution_time":0.0062,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8db64cfd"}}
{"datetime":"2025-09-02 19:36:19","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9dabc27b"}}
{"datetime":"2025-09-02 19:36:19","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":1,"ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9dabc27b"}}
{"datetime":"2025-09-02 19:36:19","level":"INFO","message":"应用结束","context":{"execution_time":0.0665,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9dabc27b"}}
{"datetime":"2025-09-02 19:36:19","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8410c947"}}
{"datetime":"2025-09-02 19:36:19","level":"INFO","message":"应用结束","context":{"execution_time":0.0212,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8410c947"}}
{"datetime":"2025-09-02 19:37:25","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"04f0a15b"}}
{"datetime":"2025-09-02 19:37:25","level":"INFO","message":"应用结束","context":{"execution_time":0.0248,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"04f0a15b"}}
{"datetime":"2025-09-02 19:43:02","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e9045fcf"}}
{"datetime":"2025-09-02 19:43:02","level":"INFO","message":"应用结束","context":{"execution_time":0.0263,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e9045fcf"}}
{"datetime":"2025-09-02 19:48:05","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fe0d6c5e"}}
{"datetime":"2025-09-02 19:48:05","level":"INFO","message":"应用结束","context":{"execution_time":0.0073,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fe0d6c5e"}}
{"datetime":"2025-09-02 19:48:05","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"00ed1034"}}
{"datetime":"2025-09-02 19:48:05","level":"INFO","message":"应用结束","context":{"execution_time":0.0094,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"00ed1034"}}
{"datetime":"2025-09-02 19:48:10","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"07524bfc"}}
{"datetime":"2025-09-02 19:48:10","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":1,"ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"07524bfc"}}
{"datetime":"2025-09-02 19:48:10","level":"INFO","message":"应用结束","context":{"execution_time":0.06,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"07524bfc"}}
{"datetime":"2025-09-02 19:48:10","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1c9c9141"}}
{"datetime":"2025-09-02 19:48:10","level":"INFO","message":"应用结束","context":{"execution_time":0.0275,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1c9c9141"}}
{"datetime":"2025-09-02 19:48:13","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2ed93d6c"}}
{"datetime":"2025-09-02 19:48:13","level":"INFO","message":"应用结束","context":{"execution_time":0.021,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"2ed93d6c"}}
{"datetime":"2025-09-02 19:48:49","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"07967a87"}}
{"datetime":"2025-09-02 19:48:49","level":"CRITICAL","message":"未捕获的异常: Call to a member function getRow() on null","context":{"message":"Call to a member function getRow() on null","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":211,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(263): GameTypeHelper::isGameTypeTableReady()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\game_records_import_export.php(293): GameTypeHelper::getGameTypes()\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"07967a87"}}
{"datetime":"2025-09-02 19:48:49","level":"INFO","message":"应用结束","context":{"execution_time":0.0083,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"07967a87"}}
{"datetime":"2025-09-02 19:49:09","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9aeb4474"}}
{"datetime":"2025-09-02 19:49:09","level":"INFO","message":"应用结束","context":{"execution_time":0.0706,"memory_peak":27262976,"memory_end":8388608},"extra":{"memory_usage":8388608,"peak_memory":27262976,"request_id":"9aeb4474"}}
{"datetime":"2025-09-02 19:51:31","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"unknown"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"4f954fb4"}}
{"datetime":"2025-09-02 19:51:31","level":"INFO","message":"应用结束","context":{"execution_time":0.039,"memory_peak":6291456,"memory_end":6291456},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"4f954fb4"}}
{"datetime":"2025-09-02 19:52:48","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8b451e63"}}
{"datetime":"2025-09-02 19:52:48","level":"INFO","message":"应用结束","context":{"execution_time":0.0138,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8b451e63"}}
{"datetime":"2025-09-02 19:52:49","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"394bfa67"}}
{"datetime":"2025-09-02 19:52:49","level":"INFO","message":"应用结束","context":{"execution_time":0.0131,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"394bfa67"}}
{"datetime":"2025-09-02 19:52:50","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fd60b9d4"}}
{"datetime":"2025-09-02 19:52:50","level":"CRITICAL","message":"未捕获的异常: Call to a member function getRow() on null","context":{"message":"Call to a member function getRow() on null","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":211,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(263): GameTypeHelper::isGameTypeTableReady()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\game_data_import_export.php(395): GameTypeHelper::getGameTypes()\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fd60b9d4"}}
{"datetime":"2025-09-02 19:52:50","level":"INFO","message":"应用结束","context":{"execution_time":0.0076,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fd60b9d4"}}
{"datetime":"2025-09-02 19:53:01","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f1ca92f6"}}
{"datetime":"2025-09-02 19:53:01","level":"CRITICAL","message":"未捕获的异常: Call to a member function getRow() on null","context":{"message":"Call to a member function getRow() on null","file":"D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php","line":211,"trace":"#0 D:\\xampp\\htdocs\\BDLX\\admin\\includes\\GameTypeHelper.php(263): GameTypeHelper::isGameTypeTableReady()\n#1 D:\\xampp\\htdocs\\BDLX\\admin\\game_data_import_export.php(395): GameTypeHelper::getGameTypes()\n#2 {main}"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f1ca92f6"}}
{"datetime":"2025-09-02 19:53:01","level":"INFO","message":"应用结束","context":{"execution_time":0.0191,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f1ca92f6"}}
{"datetime":"2025-09-02 19:53:06","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"54b31355"}}
{"datetime":"2025-09-02 19:53:06","level":"INFO","message":"应用结束","context":{"execution_time":0.0408,"memory_peak":27262976,"memory_end":8388608},"extra":{"memory_usage":8388608,"peak_memory":27262976,"request_id":"54b31355"}}
{"datetime":"2025-09-02 19:54:32","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f5c67793"}}
{"datetime":"2025-09-02 19:54:32","level":"INFO","message":"应用结束","context":{"execution_time":0.0135,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f5c67793"}}
{"datetime":"2025-09-02 19:55:41","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"733c12e3"}}
{"datetime":"2025-09-02 19:55:41","level":"INFO","message":"应用结束","context":{"execution_time":0.0245,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"733c12e3"}}
{"datetime":"2025-09-02 19:55:50","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1e00fafb"}}
{"datetime":"2025-09-02 19:55:50","level":"INFO","message":"应用结束","context":{"execution_time":0.0226,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1e00fafb"}}
{"datetime":"2025-09-02 19:55:57","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f6592bbf"}}
{"datetime":"2025-09-02 19:55:57","level":"INFO","message":"应用结束","context":{"execution_time":0.0124,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f6592bbf"}}
{"datetime":"2025-09-02 19:56:04","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bb5b9429"}}
{"datetime":"2025-09-02 19:56:04","level":"INFO","message":"应用结束","context":{"execution_time":0.0331,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bb5b9429"}}
{"datetime":"2025-09-02 20:05:22","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b2d2231f"}}
{"datetime":"2025-09-02 20:05:23","level":"INFO","message":"应用结束","context":{"execution_time":0.0198,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b2d2231f"}}
{"datetime":"2025-09-02 20:06:34","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6febdced"}}
{"datetime":"2025-09-02 20:06:34","level":"INFO","message":"应用结束","context":{"execution_time":0.0147,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6febdced"}}
{"datetime":"2025-09-02 20:08:46","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ec582b13"}}
{"datetime":"2025-09-02 20:08:46","level":"INFO","message":"应用结束","context":{"execution_time":0.0181,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ec582b13"}}
{"datetime":"2025-09-02 20:16:05","level":"INFO","message":"应用启动","context":{"memory_usage":6291456,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"ff217462"}}
{"datetime":"2025-09-02 20:16:05","level":"INFO","message":"应用结束","context":{"execution_time":0.0202,"memory_peak":6291456,"memory_end":6291456},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"ff217462"}}
{"datetime":"2025-09-02 20:50:31","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8c72c4f7"}}
{"datetime":"2025-09-02 20:50:31","level":"INFO","message":"应用结束","context":{"execution_time":0.0148,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"8c72c4f7"}}
{"datetime":"2025-09-02 20:52:57","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee96953c"}}
{"datetime":"2025-09-02 20:52:57","level":"INFO","message":"应用结束","context":{"execution_time":0.0256,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee96953c"}}
{"datetime":"2025-09-02 21:06:08","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"cf66d583"}}
{"datetime":"2025-09-02 21:06:08","level":"INFO","message":"应用结束","context":{"execution_time":0.0174,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"cf66d583"}}
{"datetime":"2025-09-02 21:11:32","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ff288448"}}
{"datetime":"2025-09-02 21:11:32","level":"INFO","message":"应用结束","context":{"execution_time":0.0234,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ff288448"}}
{"datetime":"2025-09-02 21:12:11","level":"INFO","message":"应用启动","context":{"memory_usage":6291456,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"b7e9911b"}}
{"datetime":"2025-09-02 21:12:11","level":"INFO","message":"应用结束","context":{"execution_time":0.0278,"memory_peak":6291456,"memory_end":6291456},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"b7e9911b"}}
{"datetime":"2025-09-02 21:40:33","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9ff0aaa6"}}
{"datetime":"2025-09-02 21:40:33","level":"INFO","message":"应用结束","context":{"execution_time":0.0302,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9ff0aaa6"}}
{"datetime":"2025-09-02 22:17:46","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"5422b4db"}}
{"datetime":"2025-09-02 22:17:46","level":"INFO","message":"应用结束","context":{"execution_time":0.0278,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"5422b4db"}}
{"datetime":"2025-09-02 22:33:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee6944b3"}}
{"datetime":"2025-09-02 22:33:44","level":"INFO","message":"应用结束","context":{"execution_time":0.0403,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"ee6944b3"}}
{"datetime":"2025-09-02 22:33:45","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3306c775"}}
{"datetime":"2025-09-02 22:33:45","level":"INFO","message":"应用结束","context":{"execution_time":0.0214,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3306c775"}}
{"datetime":"2025-09-02 22:33:45","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e6867211"}}
{"datetime":"2025-09-02 22:33:45","level":"INFO","message":"应用结束","context":{"execution_time":0.0229,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e6867211"}}
{"datetime":"2025-09-02 22:38:45","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"adedfd37"}}
{"datetime":"2025-09-02 22:38:45","level":"INFO","message":"应用结束","context":{"execution_time":0.0277,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"adedfd37"}}
{"datetime":"2025-09-02 22:42:26","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1f8353bd"}}
{"datetime":"2025-09-02 22:42:26","level":"INFO","message":"应用结束","context":{"execution_time":0.0265,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1f8353bd"}}
{"datetime":"2025-09-02 22:49:57","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"680ff050"}}
{"datetime":"2025-09-02 22:49:57","level":"INFO","message":"应用结束","context":{"execution_time":0.024,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"680ff050"}}
{"datetime":"2025-09-02 23:35:20","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"15c4583c"}}
{"datetime":"2025-09-02 23:35:20","level":"INFO","message":"应用结束","context":{"execution_time":0.0277,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"15c4583c"}}
{"datetime":"2025-09-02 23:56:29","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"a89bd4f3"}}
{"datetime":"2025-09-02 23:56:29","level":"INFO","message":"应用结束","context":{"execution_time":0.0246,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"a89bd4f3"}}
{"datetime":"2025-09-03 00:44:32","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6e5a5a2f"}}
{"datetime":"2025-09-03 00:44:32","level":"INFO","message":"应用结束","context":{"execution_time":0.0262,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6e5a5a2f"}}
{"datetime":"2025-09-03 00:44:32","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"5e44e65b"}}
{"datetime":"2025-09-03 00:44:33","level":"INFO","message":"应用结束","context":{"execution_time":0.015,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"5e44e65b"}}
{"datetime":"2025-09-03 20:08:17","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9db77b4a"}}
{"datetime":"2025-09-03 20:08:17","level":"INFO","message":"应用结束","context":{"execution_time":0.027,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9db77b4a"}}
{"datetime":"2025-09-03 20:08:17","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"375419dd"}}
{"datetime":"2025-09-03 20:08:17","level":"INFO","message":"应用结束","context":{"execution_time":0.006,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"375419dd"}}
{"datetime":"2025-09-03 20:08:23","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"eb43ab04"}}
{"datetime":"2025-09-03 20:08:23","level":"WARNING","message":"登录失败","context":{"username":"admin","ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"eb43ab04"}}
{"datetime":"2025-09-03 20:08:23","level":"INFO","message":"应用结束","context":{"execution_time":0.1062,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"eb43ab04"}}
{"datetime":"2025-09-03 20:08:28","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9919c924"}}
{"datetime":"2025-09-03 20:08:28","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":1,"ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9919c924"}}
{"datetime":"2025-09-03 20:08:28","level":"INFO","message":"应用结束","context":{"execution_time":0.1161,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9919c924"}}
{"datetime":"2025-09-03 20:08:28","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"818ad461"}}
{"datetime":"2025-09-03 20:08:28","level":"INFO","message":"应用结束","context":{"execution_time":0.0463,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"818ad461"}}
{"datetime":"2025-09-03 20:08:58","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"71c456b9"}}
{"datetime":"2025-09-03 20:08:58","level":"INFO","message":"应用结束","context":{"execution_time":0.0149,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"71c456b9"}}
{"datetime":"2025-09-03 20:09:08","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0dc91868"}}
{"datetime":"2025-09-03 20:09:08","level":"INFO","message":"应用结束","context":{"execution_time":0.025,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0dc91868"}}
{"datetime":"2025-09-03 20:29:31","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bd8e1d20"}}
{"datetime":"2025-09-03 20:29:31","level":"INFO","message":"应用结束","context":{"execution_time":0.0253,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bd8e1d20"}}
{"datetime":"2025-09-03 20:51:16","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c376b4e9"}}
{"datetime":"2025-09-03 20:51:16","level":"INFO","message":"应用结束","context":{"execution_time":0.0259,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"c376b4e9"}}
{"datetime":"2025-09-03 20:52:42","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"91230efb"}}
{"datetime":"2025-09-03 20:52:42","level":"INFO","message":"应用结束","context":{"execution_time":0.0238,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"91230efb"}}
{"datetime":"2025-09-03 21:01:45","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"af3350fe"}}
{"datetime":"2025-09-03 21:01:45","level":"INFO","message":"应用结束","context":{"execution_time":0.0137,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"af3350fe"}}
{"datetime":"2025-09-03 21:27:44","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f19aebb7"}}
{"datetime":"2025-09-03 21:27:44","level":"INFO","message":"应用结束","context":{"execution_time":0.0253,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f19aebb7"}}
{"datetime":"2025-09-03 21:28:07","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"83a31316"}}
{"datetime":"2025-09-03 21:28:07","level":"INFO","message":"应用结束","context":{"execution_time":0.0261,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"83a31316"}}
{"datetime":"2025-09-03 21:28:08","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"68d6fb90"}}
{"datetime":"2025-09-03 21:28:08","level":"INFO","message":"应用结束","context":{"execution_time":0.0231,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"68d6fb90"}}
{"datetime":"2025-09-03 21:29:17","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"05a3c2cc"}}
{"datetime":"2025-09-03 22:01:06","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9678e95c"}}
{"datetime":"2025-09-03 22:03:57","level":"INFO","message":"应用启动","context":{"memory_usage":14680064,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":14680064,"peak_memory":14680064,"request_id":"364b19e1"}}
{"datetime":"2025-09-03 22:03:57","level":"INFO","message":"应用结束","context":{"execution_time":0.0097,"memory_peak":14680064,"memory_end":14680064},"extra":{"memory_usage":14680064,"peak_memory":14680064,"request_id":"364b19e1"}}
{"datetime":"2025-09-03 22:03:57","level":"INFO","message":"应用启动","context":{"memory_usage":8388608,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"894c0252"}}
{"datetime":"2025-09-03 22:03:57","level":"INFO","message":"应用结束","context":{"execution_time":0.0048,"memory_peak":8388608,"memory_end":8388608},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"894c0252"}}
{"datetime":"2025-09-03 22:04:03","level":"INFO","message":"应用启动","context":{"memory_usage":14680064,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":14680064,"peak_memory":14680064,"request_id":"357fb501"}}
{"datetime":"2025-09-03 22:04:03","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":10,"ip":"::1"},"extra":{"memory_usage":14680064,"peak_memory":14680064,"request_id":"357fb501"}}
{"datetime":"2025-09-03 22:04:03","level":"INFO","message":"应用结束","context":{"execution_time":0.0609,"memory_peak":14680064,"memory_end":14680064},"extra":{"memory_usage":14680064,"peak_memory":14680064,"request_id":"357fb501"}}
{"datetime":"2025-09-03 22:04:03","level":"INFO","message":"应用启动","context":{"memory_usage":8388608,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"3213cd63"}}
{"datetime":"2025-09-03 22:04:03","level":"INFO","message":"应用结束","context":{"execution_time":0.0148,"memory_peak":8388608,"memory_end":8388608},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"3213cd63"}}
{"datetime":"2025-09-03 22:04:06","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"aa821737"}}
{"datetime":"2025-09-03 22:04:06","level":"INFO","message":"应用结束","context":{"execution_time":0.0145,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"aa821737"}}
{"datetime":"2025-09-03 22:04:12","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e9f2d77b"}}
{"datetime":"2025-09-03 22:04:12","level":"INFO","message":"应用结束","context":{"execution_time":0.014,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e9f2d77b"}}
{"datetime":"2025-09-03 22:04:13","level":"INFO","message":"应用启动","context":{"memory_usage":4194304,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"65d96b51"}}
{"datetime":"2025-09-03 22:04:14","level":"INFO","message":"应用结束","context":{"execution_time":0.0221,"memory_peak":4194304,"memory_end":4194304},"extra":{"memory_usage":4194304,"peak_memory":4194304,"request_id":"65d96b51"}}
{"datetime":"2025-09-03 22:04:14","level":"INFO","message":"应用启动","context":{"memory_usage":10485760,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":10485760,"peak_memory":10485760,"request_id":"0df7f267"}}
{"datetime":"2025-09-03 22:04:14","level":"INFO","message":"应用结束","context":{"execution_time":0.014,"memory_peak":10485760,"memory_end":10485760},"extra":{"memory_usage":10485760,"peak_memory":10485760,"request_id":"0df7f267"}}
{"datetime":"2025-09-03 22:07:19","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1245cff0"}}
{"datetime":"2025-09-03 22:07:19","level":"INFO","message":"应用结束","context":{"execution_time":0.0237,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1245cff0"}}
{"datetime":"2025-09-03 22:09:28","level":"INFO","message":"应用启动","context":{"memory_usage":6291456,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"5d88c5bf"}}
{"datetime":"2025-09-03 22:09:28","level":"INFO","message":"应用结束","context":{"execution_time":0.0147,"memory_peak":6291456,"memory_end":6291456},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"5d88c5bf"}}
{"datetime":"2025-09-03 22:11:01","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"de52d38d"}}
{"datetime":"2025-09-03 22:11:01","level":"INFO","message":"应用结束","context":{"execution_time":0.021,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"de52d38d"}}
{"datetime":"2025-09-03 22:22:46","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bf79913f"}}
{"datetime":"2025-09-03 22:22:46","level":"INFO","message":"应用结束","context":{"execution_time":0.0167,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"bf79913f"}}
{"datetime":"2025-09-03 22:22:57","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"aa7b6dda"}}
{"datetime":"2025-09-03 22:22:57","level":"INFO","message":"应用结束","context":{"execution_time":0.0263,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"aa7b6dda"}}
{"datetime":"2025-09-03 23:44:31","level":"INFO","message":"应用启动","context":{"memory_usage":6291456,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"7e3cb254"}}
{"datetime":"2025-09-03 23:44:31","level":"INFO","message":"应用结束","context":{"execution_time":0.0393,"memory_peak":6291456,"memory_end":6291456},"extra":{"memory_usage":6291456,"peak_memory":6291456,"request_id":"7e3cb254"}}
{"datetime":"2025-09-03 23:44:42","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1869cffc"}}
{"datetime":"2025-09-03 23:44:43","level":"INFO","message":"应用结束","context":{"execution_time":0.0264,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"1869cffc"}}
{"datetime":"2025-09-03 23:53:43","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9aa5f490"}}
{"datetime":"2025-09-03 23:53:43","level":"INFO","message":"应用结束","context":{"execution_time":0.0358,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"9aa5f490"}}
{"datetime":"2025-09-03 23:54:01","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d82a5256"}}
{"datetime":"2025-09-03 23:54:01","level":"INFO","message":"应用结束","context":{"execution_time":0.0234,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"d82a5256"}}
{"datetime":"2025-09-03 23:55:07","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3da6ac98"}}
{"datetime":"2025-09-03 23:55:07","level":"INFO","message":"应用结束","context":{"execution_time":0.0148,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3da6ac98"}}
{"datetime":"2025-09-03 23:59:39","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"62637985"}}
{"datetime":"2025-09-03 23:59:39","level":"INFO","message":"应用结束","context":{"execution_time":0.0198,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"62637985"}}
{"datetime":"2025-09-04 00:14:01","level":"INFO","message":"应用启动","context":{"memory_usage":8388608,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"5c34905f"}}
{"datetime":"2025-09-04 00:14:01","level":"INFO","message":"应用结束","context":{"execution_time":0.0161,"memory_peak":8388608,"memory_end":8388608},"extra":{"memory_usage":8388608,"peak_memory":8388608,"request_id":"5c34905f"}}
{"datetime":"2025-09-05 22:15:55","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f976adff"}}
{"datetime":"2025-09-05 22:15:55","level":"INFO","message":"应用结束","context":{"execution_time":0.0328,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"f976adff"}}
{"datetime":"2025-09-05 22:15:55","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b8581874"}}
{"datetime":"2025-09-05 22:15:55","level":"INFO","message":"应用结束","context":{"execution_time":0.0084,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"b8581874"}}
{"datetime":"2025-09-05 22:16:02","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"23e3c93c"}}
{"datetime":"2025-09-05 22:16:02","level":"WARNING","message":"登录失败","context":{"username":"admin","ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"23e3c93c"}}
{"datetime":"2025-09-05 22:16:02","level":"INFO","message":"应用结束","context":{"execution_time":0.1251,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"23e3c93c"}}
{"datetime":"2025-09-05 22:16:07","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6ee889fc"}}
{"datetime":"2025-09-05 22:16:07","level":"WARNING","message":"登录失败","context":{"username":"admin","ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6ee889fc"}}
{"datetime":"2025-09-05 22:16:07","level":"INFO","message":"应用结束","context":{"execution_time":0.0859,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"6ee889fc"}}
{"datetime":"2025-09-05 22:16:15","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3c13f4e2"}}
{"datetime":"2025-09-05 22:16:15","level":"WARNING","message":"登录失败","context":{"username":"admin","ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3c13f4e2"}}
{"datetime":"2025-09-05 22:16:15","level":"INFO","message":"应用结束","context":{"execution_time":0.1002,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3c13f4e2"}}
{"datetime":"2025-09-05 22:16:19","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"eff4b54f"}}
{"datetime":"2025-09-05 22:16:19","level":"WARNING","message":"登录失败","context":{"username":"admin","ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"eff4b54f"}}
{"datetime":"2025-09-05 22:16:19","level":"INFO","message":"应用结束","context":{"execution_time":0.1128,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"eff4b54f"}}
{"datetime":"2025-09-05 22:16:25","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"17c672f1"}}
{"datetime":"2025-09-05 22:16:26","level":"INFO","message":"管理员登录成功","context":{"username":"admin","user_id":10,"ip":"::1"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"17c672f1"}}
{"datetime":"2025-09-05 22:16:26","level":"INFO","message":"应用结束","context":{"execution_time":0.1211,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"17c672f1"}}
{"datetime":"2025-09-05 22:16:26","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0edc02e0"}}
{"datetime":"2025-09-05 22:16:26","level":"INFO","message":"应用结束","context":{"execution_time":0.0465,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"0edc02e0"}}
{"datetime":"2025-09-05 23:06:53","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3335b88d"}}
{"datetime":"2025-09-05 23:06:53","level":"INFO","message":"应用结束","context":{"execution_time":0.0275,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"3335b88d"}}
{"datetime":"2025-09-05 23:20:49","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fd5afc5c"}}
{"datetime":"2025-09-05 23:20:49","level":"INFO","message":"应用结束","context":{"execution_time":0.0271,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"fd5afc5c"}}
{"datetime":"2025-09-05 23:20:51","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"46560488"}}
{"datetime":"2025-09-05 23:20:51","level":"INFO","message":"应用结束","context":{"execution_time":0.0185,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"46560488"}}
{"datetime":"2025-09-05 23:23:16","level":"INFO","message":"应用启动","context":{"memory_usage":2097152,"php_version":"8.2.12","server":"Apache/2.4.58 (Win64) OpenSSL/3.1.3 PHP/8.2.12"},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e5528934"}}
{"datetime":"2025-09-05 23:23:16","level":"INFO","message":"应用结束","context":{"execution_time":0.0257,"memory_peak":2097152,"memory_end":2097152},"extra":{"memory_usage":2097152,"peak_memory":2097152,"request_id":"e5528934"}}
