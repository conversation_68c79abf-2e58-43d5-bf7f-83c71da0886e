<?php
/**
 * 七牛云同步插件管理界面
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 检查权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: ../../../admin/login.php');
    exit;
}

try {
    $qiniuSync = new QiniuSync();
    $configManager = $qiniuSync->getConfigManager();
    $syncManager = $qiniuSync->getSyncManager();
    $fileManager = $qiniuSync->getFileManager();
    $queueManager = $qiniuSync->getQueueManager();
    
    // 获取插件状态
    $status = $qiniuSync->getStatus();
    $qiniuConfig = $configManager->getQiniuConfig();
    $fileStats = $fileManager->getFileStats();
    $queueStats = $queueManager->getQueueSize();
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>七牛云同步插件 - 管理面板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .card-stat {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .card-stat .card-body {
            padding: 1.5rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .nav-pills .nav-link.active {
            background-color: #667eea;
        }
        
        .btn-primary {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
            border-color: #5a6fd8;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cloud-upload"></i> 七牛云同步插件
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../../../admin/">返回主面板</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($error)): ?>
        <div class="alert alert-danger" role="alert">
            <i class="bi bi-exclamation-triangle"></i> 错误: <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <!-- 状态概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card card-stat">
                    <div class="card-body text-center">
                        <div class="stat-number"><?php echo $fileStats['total_files'] ?? 0; ?></div>
                        <div>总文件数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stat">
                    <div class="card-body text-center">
                        <div class="stat-number"><?php echo $fileStats['synced_files'] ?? 0; ?></div>
                        <div>已同步文件</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stat">
                    <div class="card-body text-center">
                        <div class="stat-number"><?php echo $queueStats['pending'] ?? 0; ?></div>
                        <div>待处理队列</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card card-stat">
                    <div class="card-body text-center">
                        <div class="stat-number"><?php echo number_format(($fileStats['synced_size'] ?? 0) / 1024 / 1024, 2); ?>MB</div>
                        <div>已同步大小</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="row">
            <div class="col-md-3">
                <!-- 侧边栏导航 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">管理菜单</h5>
                    </div>
                    <div class="card-body p-0">
                        <ul class="nav nav-pills flex-column" id="admin-tabs">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="pill" href="#dashboard">
                                    <i class="bi bi-speedometer2"></i> 仪表板
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="pill" href="#config">
                                    <i class="bi bi-gear"></i> 配置设置
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="pill" href="#sync">
                                    <i class="bi bi-arrow-repeat"></i> 同步控制
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="pill" href="#files">
                                    <i class="bi bi-files"></i> 文件管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="pill" href="#queue">
                                    <i class="bi bi-list-task"></i> 队列管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="pill" href="#logs">
                                    <i class="bi bi-journal-text"></i> 日志查看
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="tab-content">
                    <!-- 仪表板 -->
                    <div class="tab-pane fade show active" id="dashboard">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-speedometer2"></i> 系统状态
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>插件状态</h6>
                                        <p>
                                            <span class="status-indicator <?php echo ($status['data']['enabled'] ?? false) ? 'status-running' : 'status-stopped'; ?>"></span>
                                            <?php echo ($status['data']['enabled'] ?? false) ? '已启用' : '已禁用'; ?>
                                        </p>
                                        
                                        <h6>同步状态</h6>
                                        <p>
                                            <span class="status-indicator <?php echo ($status['data']['sync_running'] ?? false) ? 'status-running' : 'status-stopped'; ?>"></span>
                                            <?php echo ($status['data']['sync_running'] ?? false) ? '运行中' : '已停止'; ?>
                                        </p>
                                        
                                        <h6>最后同步时间</h6>
                                        <p><?php echo $status['data']['last_sync'] ? date('Y-m-d H:i:s', $status['data']['last_sync']) : '从未同步'; ?></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>七牛云配置</h6>
                                        <p>
                                            <strong>Bucket:</strong> <?php echo htmlspecialchars($qiniuConfig['bucket'] ?: '未配置'); ?><br>
                                            <strong>Domain:</strong> <?php echo htmlspecialchars($qiniuConfig['domain'] ?: '未配置'); ?><br>
                                            <strong>Region:</strong> <?php echo htmlspecialchars($qiniuConfig['region'] ?: 'z0'); ?>
                                        </p>
                                        
                                        <div class="mt-3">
                                            <button class="btn btn-primary btn-sm" onclick="testConnection()">
                                                <i class="bi bi-wifi"></i> 测试连接
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 配置设置 -->
                    <div class="tab-pane fade" id="config">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-gear"></i> 七牛云配置
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="qiniu-config-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">AccessKey *</label>
                                                <input type="text" class="form-control" name="access_key" 
                                                       value="<?php echo htmlspecialchars($qiniuConfig['access_key']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">SecretKey *</label>
                                                <input type="password" class="form-control" name="secret_key" 
                                                       value="<?php echo htmlspecialchars($qiniuConfig['secret_key']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Bucket *</label>
                                                <input type="text" class="form-control" name="bucket" 
                                                       value="<?php echo htmlspecialchars($qiniuConfig['bucket']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Domain *</label>
                                                <input type="text" class="form-control" name="domain" 
                                                       value="<?php echo htmlspecialchars($qiniuConfig['domain']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">文件前缀</label>
                                                <input type="text" class="form-control" name="prefix" 
                                                       value="<?php echo htmlspecialchars($qiniuConfig['prefix']); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">区域</label>
                                                <select class="form-select" name="region">
                                                    <option value="z0" <?php echo $qiniuConfig['region'] === 'z0' ? 'selected' : ''; ?>>华东-浙江</option>
                                                    <option value="z1" <?php echo $qiniuConfig['region'] === 'z1' ? 'selected' : ''; ?>>华北-河北</option>
                                                    <option value="z2" <?php echo $qiniuConfig['region'] === 'z2' ? 'selected' : ''; ?>>华南-广东</option>
                                                    <option value="na0" <?php echo $qiniuConfig['region'] === 'na0' ? 'selected' : ''; ?>>北美-洛杉矶</option>
                                                    <option value="as0" <?php echo $qiniuConfig['region'] === 'as0' ? 'selected' : ''; ?>>亚太-新加坡</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" name="use_https" 
                                                       <?php echo $qiniuConfig['use_https'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label">使用HTTPS</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" name="use_cdn_domain" 
                                                       <?php echo $qiniuConfig['use_cdn_domain'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label">使用CDN域名</label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check"></i> 保存配置
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="validateConfig()">
                                            <i class="bi bi-check-circle"></i> 验证配置
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- 本地同步目录配置 -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-folder"></i> 本地同步目录配置
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="sync-dirs-form">
                                    <div class="mb-3">
                                        <label class="form-label">同步目录列表</label>
                                        <div id="sync-dirs-container">
                                            <?php
                                            $syncDirs = $configManager->getConfig('sync.directories', [
                                                QINIU_SYNC_ROOT . '/uploads',
                                                QINIU_SYNC_ROOT . '/images'
                                            ]);
                                            foreach ($syncDirs as $index => $dir):
                                            ?>
                                            <div class="input-group mb-2" data-index="<?php echo $index; ?>">
                                                <input type="text" class="form-control" name="sync_dirs[]"
                                                       value="<?php echo htmlspecialchars($dir); ?>"
                                                       placeholder="输入目录路径，如: /var/www/uploads">
                                                <button type="button" class="btn btn-outline-danger" onclick="removeSyncDir(this)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addSyncDir()">
                                            <i class="bi bi-plus"></i> 添加目录
                                        </button>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">允许的文件扩展名</label>
                                                <input type="text" class="form-control" name="allowed_extensions"
                                                       value="<?php echo htmlspecialchars(implode(',', $configManager->getConfig('sync.allowed_extensions', ['jpg','jpeg','png','gif','webp','pdf','txt']))); ?>"
                                                       placeholder="jpg,jpeg,png,gif,webp,pdf,txt">
                                                <small class="form-text text-muted">用逗号分隔，不包含点号</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">最大文件大小 (MB)</label>
                                                <input type="number" class="form-control" name="max_file_size"
                                                       value="<?php echo $configManager->getConfig('sync.max_file_size', 10); ?>"
                                                       min="1" max="100">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" name="auto_scan"
                                                       <?php echo $configManager->getConfig('sync.auto_scan', true) ? 'checked' : ''; ?>>
                                                <label class="form-check-label">自动扫描新文件</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" name="delete_after_sync"
                                                       <?php echo $configManager->getConfig('sync.delete_after_sync', false) ? 'checked' : ''; ?>>
                                                <label class="form-check-label">同步后删除本地文件</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check"></i> 保存目录配置
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="scanAllDirs()">
                                            <i class="bi bi-search"></i> 扫描所有目录
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 同步控制 -->
                    <div class="tab-pane fade" id="sync">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-arrow-repeat"></i> 同步控制
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>同步服务控制</h6>
                                        <div class="d-flex gap-2 mb-3">
                                            <button class="btn btn-success" onclick="startSync()">
                                                <i class="bi bi-play"></i> 启动同步
                                            </button>
                                            <button class="btn btn-danger" onclick="stopSync()">
                                                <i class="bi bi-stop"></i> 停止同步
                                            </button>
                                        </div>
                                        
                                        <h6>手动操作</h6>
                                        <div class="d-flex gap-2 mb-2">
                                            <button class="btn btn-primary" onclick="manualSync()">
                                                <i class="bi bi-cloud-upload"></i> 手动同步
                                            </button>
                                            <button class="btn btn-info" onclick="syncAllFiles()">
                                                <i class="bi bi-arrow-repeat"></i> 全量同步
                                            </button>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-warning" onclick="cleanupFiles()">
                                                <i class="bi bi-trash"></i> 清理失效文件
                                            </button>
                                            <button class="btn btn-secondary" onclick="refreshStatus()">
                                                <i class="bi bi-arrow-clockwise"></i> 刷新状态
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>同步统计</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <tr>
                                                    <td>总文件数:</td>
                                                    <td><?php echo $fileStats['total_files'] ?? 0; ?></td>
                                                </tr>
                                                <tr>
                                                    <td>已同步:</td>
                                                    <td><?php echo $fileStats['synced_files'] ?? 0; ?></td>
                                                </tr>
                                                <tr>
                                                    <td>失败:</td>
                                                    <td><?php echo $fileStats['failed_files'] ?? 0; ?></td>
                                                </tr>
                                                <tr>
                                                    <td>待处理:</td>
                                                    <td><?php echo $fileStats['pending_files'] ?? 0; ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 文件管理标签页 -->
                    <div class="tab-pane fade" id="files">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-files"></i> 文件管理
                                </h5>
                                <a href="files.php" class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="bi bi-box-arrow-up-right"></i> 在新窗口打开
                                </a>
                            </div>
                            <div class="card-body">
                                <div id="files-content">
                                    <iframe src="files.php" width="100%" height="600" frameborder="0" style="border-radius: 0.375rem;"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 队列管理标签页 -->
                    <div class="tab-pane fade" id="queue">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-list-task"></i> 队列管理
                                </h5>
                                <a href="queue.php" class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="bi bi-box-arrow-up-right"></i> 在新窗口打开
                                </a>
                            </div>
                            <div class="card-body">
                                <div id="queue-content">
                                    <iframe src="queue.php" width="100%" height="600" frameborder="0" style="border-radius: 0.375rem;"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 日志查看标签页 -->
                    <div class="tab-pane fade" id="logs">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-journal-text"></i> 日志查看
                                </h5>
                                <a href="logs.php" class="btn btn-outline-primary btn-sm" target="_blank">
                                    <i class="bi bi-box-arrow-up-right"></i> 在新窗口打开
                                </a>
                            </div>
                            <div class="card-body">
                                <div id="logs-content">
                                    <iframe src="logs.php" width="100%" height="600" frameborder="0" style="border-radius: 0.375rem;"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">消息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="modal-message"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 显示消息
        function showMessage(message, isSuccess = false) {
            const messageElement = document.getElementById('modal-message');
            messageElement.innerHTML = message;

            // 根据消息类型设置样式
            const modalHeader = document.querySelector('#messageModal .modal-header');
            if (isSuccess) {
                modalHeader.className = 'modal-header bg-success text-white';
            } else {
                modalHeader.className = 'modal-header bg-danger text-white';
            }

            const modal = new bootstrap.Modal(document.getElementById('messageModal'));
            modal.show();
        }

        // 测试连接
        function testConnection() {
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'test_connection'})
            })
            .then(response => response.json())
            .then(data => {
                showMessage(data.success ? '连接测试成功' : '连接测试失败: ' + data.error);
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        }

        // 保存配置
        document.getElementById('qiniu-config-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const config = Object.fromEntries(formData);
            config.use_https = formData.has('use_https');
            config.use_cdn_domain = formData.has('use_cdn_domain');

            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'save_config', config: config})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('配置保存成功', true);
                } else {
                    showMessage('配置保存失败: ' + (data.error || data.message || '未知错误'));
                }
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        });

        // 验证配置
        function validateConfig() {
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'validate_config'})
            })
            .then(response => response.json())
            .then(data => {
                if (data.valid) {
                    showMessage('配置验证成功', true);
                } else {
                    let errorMsg = '配置验证失败';
                    if (data.errors && Array.isArray(data.errors)) {
                        errorMsg += ': ' + data.errors.join(', ');
                    } else if (data.error) {
                        errorMsg += ': ' + data.error;
                    }

                    // 如果需要安装SDK，显示特殊提示
                    if (data.need_install_sdk) {
                        errorMsg += '<br><br><a href="../install_sdk.php" class="btn btn-primary btn-sm" target="_blank"><i class="bi bi-download"></i> 安装七牛云SDK</a>';
                    }

                    showMessage(errorMsg);
                }
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        }

        // 启动同步
        function startSync() {
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'start_sync'})
            })
            .then(response => response.json())
            .then(data => {
                showMessage(data.success ? '同步服务启动成功' : '启动失败: ' + data.message);
                if (data.success) setTimeout(() => location.reload(), 2000);
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        }

        // 停止同步
        function stopSync() {
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'stop_sync'})
            })
            .then(response => response.json())
            .then(data => {
                showMessage(data.success ? '同步服务停止成功' : '停止失败: ' + data.message);
                if (data.success) setTimeout(() => location.reload(), 2000);
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        }

        // 手动同步
        function manualSync() {
            const limit = prompt('请输入要同步的文件数量（默认10个）:', '10');
            if (limit === null) return;

            const numLimit = parseInt(limit) || 10;

            showMessage('正在执行手动同步，请稍候...', true);

            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'manual_sync', limit: numLimit})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`同步完成: 处理 ${data.processed || 0} 个文件, 成功 ${data.successful || 0} 个, 失败 ${data.failed || 0} 个`, true);
                    setTimeout(() => location.reload(), 3000);
                } else {
                    showMessage('同步失败: ' + data.message);
                }
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        }

        // 刷新状态
        function refreshStatus() {
            location.reload();
        }

        // 全量同步
        function syncAllFiles() {
            if (!confirm('确定要进行全量同步吗？这可能需要较长时间。')) return;

            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'sync_all_files'})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`全量同步完成。总文件: ${data.total_files}, 成功: ${data.success_count}, 失败: ${data.fail_count}`);
                } else {
                    showMessage('全量同步失败: ' + data.error);
                }
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        }

        // 清理失效文件
        function cleanupFiles() {
            if (!confirm('确定要清理失效文件记录吗？')) return;

            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'cleanup_files'})
            })
            .then(response => response.json())
            .then(data => {
                showMessage(`清理完成，删除了 ${data.deleted_count} 条失效记录`);
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        }

        // 标签页切换时加载内容
        document.addEventListener('DOMContentLoaded', function() {
            const tabLinks = document.querySelectorAll('#admin-tabs .nav-link');
            tabLinks.forEach(link => {
                link.addEventListener('shown.bs.tab', function(e) {
                    const target = e.target.getAttribute('href').substring(1);
                    loadTabContent(target);
                });
            });
        });

        function loadTabContent(tab) {
            const contentDiv = document.getElementById(tab + '-content');
            if (!contentDiv) return;

            switch(tab) {
                case 'files':
                    loadFilesContent();
                    break;
                case 'queue':
                    loadQueueContent();
                    break;
                case 'logs':
                    loadLogsContent();
                    break;
            }
        }

        function loadFilesContent() {
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'get_files'})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('files-content').innerHTML = data.html || '<p>暂无数据</p>';
            })
            .catch(error => {
                document.getElementById('files-content').innerHTML = '<p>加载失败: ' + error.message + '</p>';
            });
        }

        function loadQueueContent() {
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'get_queue'})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('queue-content').innerHTML = data.html || '<p>暂无数据</p>';
            })
            .catch(error => {
                document.getElementById('queue-content').innerHTML = '<p>加载失败: ' + error.message + '</p>';
            });
        }

        function loadLogsContent() {
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'get_logs'})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('logs-content').innerHTML = data.html || '<p>暂无数据</p>';
            })
            .catch(error => {
                document.getElementById('logs-content').innerHTML = '<p>加载失败: ' + error.message + '</p>';
            });
        }

        // 同步目录配置相关函数
        function addSyncDir() {
            const container = document.getElementById('sync-dirs-container');
            const index = container.children.length;
            const div = document.createElement('div');
            div.className = 'input-group mb-2';
            div.setAttribute('data-index', index);
            div.innerHTML = `
                <input type="text" class="form-control" name="sync_dirs[]"
                       placeholder="输入目录路径，如: /var/www/uploads">
                <button type="button" class="btn btn-outline-danger" onclick="removeSyncDir(this)">
                    <i class="bi bi-trash"></i>
                </button>
            `;
            container.appendChild(div);
        }

        function removeSyncDir(button) {
            const container = document.getElementById('sync-dirs-container');
            if (container.children.length > 1) {
                button.closest('.input-group').remove();
            } else {
                alert('至少需要保留一个同步目录');
            }
        }

        function scanAllDirs() {
            if (!confirm('确定要扫描所有配置的目录吗？这将添加新发现的文件到同步队列。')) return;

            showMessage('正在扫描目录，请稍候...', true);

            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'scan_all_dirs'})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`扫描完成！发现 ${data.total || 0} 个文件，添加 ${data.added || 0} 个新文件到同步队列。`, true);
                } else {
                    showMessage('扫描失败: ' + data.message);
                }
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        }

        // 同步目录配置表单提交
        document.getElementById('sync-dirs-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {
                action: 'save_sync_dirs',
                sync_dirs: formData.getAll('sync_dirs[]').filter(dir => dir.trim() !== ''),
                allowed_extensions: formData.get('allowed_extensions').split(',').map(ext => ext.trim()).filter(ext => ext !== ''),
                max_file_size: parseInt(formData.get('max_file_size')) || 10,
                auto_scan: formData.get('auto_scan') === 'on',
                delete_after_sync: formData.get('delete_after_sync') === 'on'
            };

            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                showMessage(data.success ? '目录配置保存成功' : '保存失败: ' + data.message, data.success);
                if (data.success) setTimeout(() => location.reload(), 2000);
            })
            .catch(error => showMessage('请求失败: ' + error.message));
        });
    </script>
</body>
</html>
