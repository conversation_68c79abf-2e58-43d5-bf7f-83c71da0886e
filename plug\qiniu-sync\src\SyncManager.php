<?php
/**
 * 七牛云同步插件同步管理器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class QiniuSyncSyncManager
{
    private $db;
    private $config;
    private $logger;
    private $configManager;
    private $fileManager;
    private $queueManager;
    private $qiniuAuth;
    private $uploadManager;
    private $bucketManager;
    private $isRunning = false;
    private $stats = [];
    
    /**
     * 构造函数
     */
    public function __construct($db, $config, $logger)
    {
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
        
        $this->configManager = new QiniuSyncConfigManager($db, $config, $logger);
        $this->fileManager = new QiniuSyncFileManager($db, $config, $logger);

        // 从配置中读取运行状态
        $this->isRunning = $this->configManager->getConfig('sync.enabled', false);

        $this->initializeQiniu();
        $this->initializeStats();
    }
    
    /**
     * 初始化七牛云
     */
    private function initializeQiniu()
    {
        try {
            $qiniuConfig = $this->configManager->getQiniuConfig();

            if (empty($qiniuConfig['access_key']) || empty($qiniuConfig['secret_key'])) {
                // 如果配置不完整，只记录警告，不抛出异常
                $this->logger->warning('七牛云配置不完整，部分功能将不可用');
                return;
            }

            $this->qiniuAuth = new \Qiniu\Auth($qiniuConfig['access_key'], $qiniuConfig['secret_key']);
            $this->uploadManager = new \Qiniu\Storage\UploadManager();
            $this->bucketManager = new \Qiniu\Storage\BucketManager($this->qiniuAuth);

        } catch (Exception $e) {
            $this->logger->error('初始化七牛云失败', ['error' => $e->getMessage()]);
            // 不抛出异常，允许插件在没有七牛云配置时也能初始化
        }
    }
    
    /**
     * 初始化统计信息
     */
    private function initializeStats()
    {
        $this->stats = [
            'start_time' => null,
            'total_processed' => 0,
            'successful_uploads' => 0,
            'failed_uploads' => 0,
            'total_size' => 0,
            'last_sync_time' => null
        ];
    }
    
    /**
     * 开始同步服务
     */
    public function start()
    {
        if ($this->isRunning) {
            return ['success' => false, 'message' => '同步服务已在运行中'];
        }

        try {
            // 标记服务为启动状态
            $this->isRunning = true;
            $this->stats['start_time'] = time();

            // 更新配置中的运行状态
            $this->updateSyncStatus(true);

            $this->logger->info('同步服务启动');

            // 不在这里启动长时间运行的服务，而是标记为启动状态
            // 实际的文件监控和队列处理将通过定时任务或其他方式处理

            return ['success' => true, 'message' => '同步服务启动成功'];
        } catch (Exception $e) {
            $this->isRunning = false;
            $this->updateSyncStatus(false);
            $this->logger->error('同步服务启动失败', ['error' => $e->getMessage()]);
            return ['success' => false, 'message' => '同步服务启动失败: ' . $e->getMessage()];
        }
    }

    /**
     * 更新同步状态到配置
     */
    private function updateSyncStatus($isRunning)
    {
        try {
            if ($this->configManager) {
                $this->configManager->updateConfig('sync.enabled', $isRunning);
                $this->configManager->updateConfig('sync.last_start_time', $isRunning ? date('Y-m-d H:i:s') : null);
            }
        } catch (Exception $e) {
            $this->logger->error('更新同步状态失败', ['error' => $e->getMessage()]);
        }
    }
    
    /**
     * 停止同步服务
     */
    public function stop()
    {
        $this->isRunning = false;
        $this->updateSyncStatus(false);
        $this->logger->info('同步服务已停止');

        return ['success' => true, 'message' => '同步服务已停止'];
    }

    /**
     * 手动执行同步
     */
    public function manualSync($limit = 10)
    {
        try {
            if (!$this->isRunning) {
                return ['success' => false, 'message' => '同步服务未启动'];
            }

            $this->logger->info('开始手动同步', ['limit' => $limit]);

            // 获取待同步文件
            $pendingFiles = $this->fileManager->getPendingFiles($limit);

            if (empty($pendingFiles)) {
                return ['success' => true, 'message' => '没有待同步的文件', 'processed' => 0];
            }

            $processed = 0;
            $successful = 0;
            $failed = 0;

            foreach ($pendingFiles as $file) {
                try {
                    $result = $this->syncFile($file);
                    $processed++;

                    if ($result['success']) {
                        $successful++;
                        $this->logger->info('文件同步成功', ['file' => $file['local_path']]);
                    } else {
                        $failed++;
                        $this->logger->error('文件同步失败', [
                            'file' => $file['local_path'],
                            'error' => $result['message']
                        ]);
                    }
                } catch (Exception $e) {
                    $failed++;
                    $this->logger->error('文件同步异常', [
                        'file' => $file['local_path'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $message = "同步完成: 处理 {$processed} 个文件, 成功 {$successful} 个, 失败 {$failed} 个";

            return [
                'success' => true,
                'message' => $message,
                'processed' => $processed,
                'successful' => $successful,
                'failed' => $failed
            ];

        } catch (Exception $e) {
            $this->logger->error('手动同步失败', ['error' => $e->getMessage()]);
            return ['success' => false, 'message' => '同步失败: ' . $e->getMessage()];
        }
    }

    /**
     * 同步单个文件
     */
    private function syncFile($file)
    {
        try {
            // 检查本地文件是否存在
            if (!file_exists($file['local_path'])) {
                $this->fileManager->updateFileStatus($file['id'], 'failed', '本地文件不存在');
                return ['success' => false, 'message' => '本地文件不存在'];
            }

            // 上传到七牛云
            $uploadResult = $this->uploadToQiniu($file['local_path'], $file['qiniu_key']);

            if ($uploadResult['success']) {
                $this->fileManager->updateFileStatus($file['id'], 'synced', null, $uploadResult['url']);
                $this->stats['successful_uploads']++;
                return ['success' => true, 'message' => '上传成功', 'url' => $uploadResult['url']];
            } else {
                $this->fileManager->updateFileStatus($file['id'], 'failed', $uploadResult['message']);
                $this->stats['failed_uploads']++;
                return ['success' => false, 'message' => $uploadResult['message']];
            }

        } catch (Exception $e) {
            $this->fileManager->updateFileStatus($file['id'], 'failed', $e->getMessage());
            $this->stats['failed_uploads']++;
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 上传文件到七牛云
     */
    private function uploadToQiniu($localPath, $qiniuKey)
    {
        try {
            // 检查七牛云SDK是否可用
            $vendorPath = dirname(__DIR__) . '/vendor/autoload.php';
            if (!file_exists($vendorPath)) {
                return ['success' => false, 'message' => '七牛云SDK未安装'];
            }

            require_once $vendorPath;

            // 获取七牛云配置
            $accessKey = $this->configManager->getConfig('qiniu.access_key');
            $secretKey = $this->configManager->getConfig('qiniu.secret_key');
            $bucket = $this->configManager->getConfig('qiniu.bucket');
            $domain = $this->configManager->getConfig('qiniu.domain');

            if (empty($accessKey) || empty($secretKey) || empty($bucket)) {
                return ['success' => false, 'message' => '七牛云配置不完整'];
            }

            // 创建认证对象
            $auth = new \Qiniu\Auth($accessKey, $secretKey);

            // 生成上传Token
            $token = $auth->uploadToken($bucket);

            // 创建上传管理器
            $uploadMgr = new \Qiniu\Storage\UploadManager();

            // 执行上传
            list($ret, $err) = $uploadMgr->putFile($token, $qiniuKey, $localPath);

            if ($err !== null) {
                return ['success' => false, 'message' => '上传失败: ' . $err->message()];
            }

            // 构建访问URL
            $protocol = $this->configManager->getConfig('qiniu.use_https', true) ? 'https' : 'http';
            $url = $protocol . '://' . $domain . '/' . $qiniuKey;

            return [
                'success' => true,
                'message' => '上传成功',
                'url' => $url,
                'key' => $qiniuKey,
                'hash' => $ret['hash'] ?? null
            ];

        } catch (Exception $e) {
            return ['success' => false, 'message' => '上传异常: ' . $e->getMessage()];
        }
    }
    
    /**
     * 启动文件监控
     */
    private function startFileWatcher()
    {
        $this->fileManager->watchFiles(function($file, $action) {
            $this->handleFileChange($file, $action);
        });
    }
    
    /**
     * 处理文件变化
     */
    private function handleFileChange($file, $action)
    {
        try {
            switch ($action) {
                case 'created':
                case 'modified':
                    $this->queueFileForUpload($file);
                    break;
                case 'deleted':
                    $this->queueFileForDeletion($file);
                    break;
            }
        } catch (Exception $e) {
            $this->logger->error('处理文件变化失败', [
                'file' => $file['local_path'],
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 将文件加入上传队列
     */
    private function queueFileForUpload($file)
    {
        if (!$this->queueManager) {
            require_once QINIU_SYNC_PATH . '/src/QueueManager.php';
            $this->queueManager = new QiniuSyncQueueManager($this->db, $this->config, $this->logger);
        }
        
        $this->queueManager->addToQueue($file['local_path'], 'upload', [
            'file_hash' => $file['hash'],
            'file_size' => $file['size'],
            'modified_time' => $file['modified']
        ]);
    }
    
    /**
     * 将文件加入删除队列
     */
    private function queueFileForDeletion($file)
    {
        if (!$this->queueManager) {
            require_once QINIU_SYNC_PATH . '/src/QueueManager.php';
            $this->queueManager = new QiniuSyncQueueManager($this->db, $this->config, $this->logger);
        }
        
        $this->queueManager->addToQueue($file['local_path'], 'delete');
    }
    
    /**
     * 启动队列处理器
     */
    private function startQueueProcessor()
    {
        if (!$this->queueManager) {
            require_once QINIU_SYNC_PATH . '/src/QueueManager.php';
            $this->queueManager = new QiniuSyncQueueManager($this->db, $this->config, $this->logger);
        }
        
        while ($this->isRunning) {
            try {
                $jobs = $this->queueManager->getNextJobs(3); // 并发处理3个任务
                
                if (empty($jobs)) {
                    sleep(5); // 没有任务时休眠5秒
                    continue;
                }
                
                foreach ($jobs as $job) {
                    $this->processQueueJob($job);
                }
                
            } catch (Exception $e) {
                $this->logger->error('队列处理出错', ['error' => $e->getMessage()]);
                sleep(10);
            }
        }
    }
    
    /**
     * 处理队列任务
     */
    private function processQueueJob($job)
    {
        try {
            $this->queueManager->markJobAsProcessing($job['id']);
            
            switch ($job['action']) {
                case 'upload':
                    $result = $this->uploadFile($job);
                    break;
                case 'delete':
                    $result = $this->deleteFile($job);
                    break;
                default:
                    throw new Exception('未知的任务类型: ' . $job['action']);
            }
            
            if ($result['success']) {
                $this->queueManager->markJobAsCompleted($job['id']);
                $this->stats['successful_uploads']++;
            } else {
                $this->queueManager->markJobAsFailed($job['id'], $result['error']);
                $this->stats['failed_uploads']++;
            }
            
            $this->stats['total_processed']++;
            $this->stats['last_sync_time'] = time();
            
        } catch (Exception $e) {
            $this->queueManager->markJobAsFailed($job['id'], $e->getMessage());
            $this->stats['failed_uploads']++;
            $this->logger->error('处理队列任务失败', [
                'job_id' => $job['id'],
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 上传文件到七牛云
     */
    public function uploadFile($job)
    {
        try {
            $localPath = $job['file_path'];
            $fullPath = QINIU_SYNC_ROOT . '/' . ltrim($localPath, '/');
            
            if (!file_exists($fullPath)) {
                throw new Exception('本地文件不存在: ' . $fullPath);
            }
            
            // 检查文件是否已同步且未变化
            if ($this->fileManager->isFileSynced($localPath)) {
                return ['success' => true, 'message' => '文件已同步，跳过'];
            }
            
            $qiniuConfig = $this->configManager->getQiniuConfig();
            $qiniuKey = $this->fileManager->generateQiniuKey($localPath, $qiniuConfig['prefix']);
            
            // 生成上传凭证
            $token = $this->qiniuAuth->uploadToken($qiniuConfig['bucket']);
            
            // 记录文件信息
            $fileHash = $this->fileManager->calculateFileHash($fullPath);
            $fileSize = filesize($fullPath);
            $mimeType = $this->fileManager->getMimeType($fullPath);
            
            $fileId = $this->fileManager->recordFile($localPath, $qiniuKey, null, $fileHash, $fileSize, $mimeType);
            
            if (!$fileId) {
                throw new Exception('记录文件信息失败');
            }
            
            // 上传文件
            list($ret, $err) = $this->uploadManager->putFile($token, $qiniuKey, $fullPath);
            
            if ($err !== null) {
                $errorMessage = $err->message();
                $this->fileManager->updateSyncStatus($fileId, 'failed', null, $errorMessage);
                throw new Exception('上传失败: ' . $errorMessage);
            }
            
            // 生成访问URL
            $qiniuUrl = $this->generateAccessUrl($qiniuKey, $qiniuConfig);
            
            // 更新同步状态
            $this->fileManager->updateSyncStatus($fileId, 'success', $qiniuUrl);
            
            $this->stats['total_size'] += $fileSize;
            
            $this->logger->info('文件上传成功', [
                'file_id' => $fileId,
                'local_path' => $localPath,
                'qiniu_key' => $qiniuKey,
                'qiniu_url' => $qiniuUrl,
                'file_size' => $fileSize
            ]);
            
            return [
                'success' => true,
                'file_id' => $fileId,
                'qiniu_url' => $qiniuUrl,
                'file_size' => $fileSize
            ];
            
        } catch (Exception $e) {
            $this->logger->error('文件上传失败', [
                'local_path' => $localPath,
                'error' => $e->getMessage()
            ]);
            
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 从七牛云删除文件
     */
    public function deleteFile($job)
    {
        try {
            $localPath = $job['file_path'];
            
            // 获取文件信息
            $fileId = $this->fileManager->getFileId($localPath);
            if (!$fileId) {
                return ['success' => true, 'message' => '文件记录不存在，跳过删除'];
            }
            
            $fileInfo = $this->fileManager->getFileInfo($fileId);
            if (!$fileInfo) {
                return ['success' => true, 'message' => '文件信息不存在，跳过删除'];
            }
            
            $qiniuConfig = $this->configManager->getQiniuConfig();
            
            // 从七牛云删除文件
            $err = $this->bucketManager->delete($qiniuConfig['bucket'], $fileInfo['qiniu_key']);
            
            if ($err !== null) {
                throw new Exception('删除失败: ' . $err->message());
            }
            
            // 删除本地记录
            $this->fileManager->deleteFileRecord($localPath);
            
            $this->logger->info('文件删除成功', [
                'local_path' => $localPath,
                'qiniu_key' => $fileInfo['qiniu_key']
            ]);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            $this->logger->error('文件删除失败', [
                'local_path' => $localPath,
                'error' => $e->getMessage()
            ]);
            
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 生成访问URL
     */
    private function generateAccessUrl($qiniuKey, $qiniuConfig)
    {
        $domain = rtrim($qiniuConfig['domain'], '/');
        $protocol = $qiniuConfig['use_https'] ? 'https' : 'http';
        
        return $protocol . '://' . $domain . '/' . $qiniuKey;
    }
    
    /**
     * 手动同步所有文件
     */
    public function syncAllFiles()
    {
        try {
            $this->logger->info('开始手动同步所有文件');
            
            $files = $this->fileManager->scanWatchPaths();
            $totalFiles = count($files);
            $processedFiles = 0;
            $successCount = 0;
            $failCount = 0;
            
            foreach ($files as $file) {
                if (!$this->fileManager->isFileSynced($file['local_path'])) {
                    $result = $this->uploadFile(['file_path' => $file['local_path']]);
                    
                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                }
                
                $processedFiles++;
                
                // 每处理10个文件记录一次进度
                if ($processedFiles % 10 === 0) {
                    $this->logger->info('同步进度', [
                        'processed' => $processedFiles,
                        'total' => $totalFiles,
                        'success' => $successCount,
                        'failed' => $failCount
                    ]);
                }
            }
            
            $this->logger->info('手动同步完成', [
                'total_files' => $totalFiles,
                'processed_files' => $processedFiles,
                'success_count' => $successCount,
                'fail_count' => $failCount
            ]);
            
            return [
                'success' => true,
                'total_files' => $totalFiles,
                'processed_files' => $processedFiles,
                'success_count' => $successCount,
                'fail_count' => $failCount
            ];
            
        } catch (Exception $e) {
            $this->logger->error('手动同步失败', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 检查同步是否运行中
     */
    public function isRunning()
    {
        // 从配置中读取运行状态，而不是依赖实例变量
        if ($this->configManager) {
            $enabled = $this->configManager->getConfig('sync.enabled', false);
            $this->isRunning = $enabled; // 同步实例变量
            return $enabled;
        }
        return $this->isRunning;
    }
    
    /**
     * 获取最后同步时间
     */
    public function getLastSyncTime()
    {
        return $this->stats['last_sync_time'];
    }
    
    /**
     * 获取同步统计
     */
    public function getStats()
    {
        return $this->stats;
    }
    
    /**
     * 测试七牛云连接
     */
    public function testConnection()
    {
        try {
            $qiniuConfig = $this->configManager->getQiniuConfig();
            
            // 获取存储空间列表
            list($buckets, $err) = $this->bucketManager->buckets();
            
            if ($err !== null) {
                throw new Exception('连接失败: ' . $err->message());
            }
            
            // 检查指定的bucket是否存在
            if (!in_array($qiniuConfig['bucket'], $buckets)) {
                throw new Exception('指定的存储空间不存在: ' . $qiniuConfig['bucket']);
            }
            
            return ['success' => true, 'message' => '连接测试成功'];
            
        } catch (Exception $e) {
            $this->logger->error('连接测试失败', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
