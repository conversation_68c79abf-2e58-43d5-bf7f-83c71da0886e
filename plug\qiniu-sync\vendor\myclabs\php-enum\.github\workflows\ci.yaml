name: "CI"

on:
  pull_request:
  push:
    branches:
      - "master"

jobs:
  phpunit:
    name: "PHPUnit"
    runs-on: "ubuntu-20.04"

    strategy:
      matrix:
        php-version:
          - "7.3"
          - "7.4"
          - "8.0"
          - "8.1"
          - "8.2"
        dependencies:
          - "highest"
        include:
          - dependencies: "lowest"
            php-version: "7.3"

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"
        with:
          fetch-depth: 2

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          php-version: "${{ matrix.php-version }}"
          ini-values: "zend.assertions=1"

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v1"
        with:
          dependency-versions: "${{ matrix.dependencies }}"

      - name: "Run PHPUnit"
        run: "vendor/bin/phpunit"
