name: <PERSON><PERSON>I with Composer

on: [push, pull_request]

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        php-versions: ['5.3', '5.4', '5.5', '5.6', '7.0', '7.1', '7.2', '7.3', '7.4', '8.0', '8.1', '8.2']
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup php for mock server
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'

      - name: Install Go
        uses: actions/setup-go@v2
        with:
          go-version: '1.21.x'

      - name: Setup build-in server
        run: |
          nohup php -S localhost:9000 -t ./tests/mock-server/ > phpd.log 2>&1 &
          echo $! > mock-server.pid

          cd tests/socks5-server/
          nohup go run main.go > ../../socks5.log 2>&1 &
          echo $! > ../../socks-server.pid

      - name: Setup php
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}

      - name: Install dependencies
        run: |
          composer self-update
          composer install --no-interaction --prefer-source --dev

      - name: Run cases
        run: |
          ./vendor/bin/phpcs --standard=PSR2 src
          ./vendor/bin/phpcs --standard=PSR2 examples
          ./vendor/bin/phpcs --standard=PSR2 tests
          ./vendor/bin/phpunit --coverage-clover=coverage.xml
          cat mock-server.pid | xargs kill
          cat socks-server.pid | xargs kill

        env:
          QINIU_ACCESS_KEY: ${{ secrets.QINIU_ACCESS_KEY }}
          QINIU_SECRET_KEY: ${{ secrets.QINIU_SECRET_KEY }}
          QINIU_TEST_BUCKET: ${{ secrets.QINIU_TEST_BUCKET }}
          QINIU_TEST_DOMAIN: ${{ secrets.QINIU_TEST_DOMAIN }}

      - name: Print mock server log
        if: ${{ failure() }}
        run: |
          cat phpd.log

      - name: Print socks5 server log
        if: ${{ failure() }}
        run: |
          cat socks5.log

      - name: After_success
        run: bash <(curl -s https://codecov.io/bash)
