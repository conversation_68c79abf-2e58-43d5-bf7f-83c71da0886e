<?php return array(
    'root' => array(
        'name' => 'bdlx/qiniu-sync-plugin',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'bdlx/qiniu-sync-plugin' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.8.5',
            'version' => '1.8.5.0',
            'reference' => 'e7be26966b7398204a234f8673fdad5ac6277802',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'qiniu/php-sdk' => array(
            'pretty_version' => 'v7.14.0',
            'version' => '7.14.0.0',
            'reference' => 'ee752ffa7263ce99fca0bd7340cf13c486a3516c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../qiniu/php-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
