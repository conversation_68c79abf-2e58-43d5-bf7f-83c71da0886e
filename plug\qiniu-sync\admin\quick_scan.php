<?php
/**
 * 快速扫描处理器 - 用于测试和快速扫描
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 设置较短的执行时间限制
set_time_limit(30);
ini_set('memory_limit', '128M');

// 自动设置登录状态
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_username'] = 'admin';

try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }
    
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['action'])) {
        throw new Exception('缺少action参数');
    }
    
    $action = $data['action'];
    $qiniuSync = new QiniuSync();
    $fileManager = $qiniuSync->getFileManager();
    $configManager = $qiniuSync->getConfigManager();
    
    switch ($action) {
        case 'quick_scan':
            $result = quickScanDirectories($fileManager, $configManager);
            echo json_encode($result);
            break;
            
        case 'test_scan':
            $result = testScanSingleDir($fileManager, $data['directory'] ?? '');
            echo json_encode($result);
            break;
            
        default:
            throw new Exception('未知操作: ' . $action);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}

/**
 * 快速扫描目录（限制文件数量）
 */
function quickScanDirectories($fileManager, $configManager)
{
    try {
        $scanDirs = $configManager->getConfig('sync.directories', []);
        $allowedExtensions = $configManager->getConfig('sync.allowed_extensions', ['jpg', 'jpeg', 'png', 'gif']);
        
        if (empty($scanDirs)) {
            return [
                'success' => false,
                'message' => '未配置同步目录'
            ];
        }
        
        $totalFiles = 0;
        $addedFiles = 0;
        $scannedDirs = [];
        
        foreach ($scanDirs as $dir) {
            $result = quickScanSingleDir($dir, $fileManager, $allowedExtensions, 50); // 每个目录最多50个文件
            
            $totalFiles += $result['total'];
            $addedFiles += $result['added'];
            
            $scannedDirs[] = [
                'path' => $dir,
                'exists' => $result['exists'],
                'total' => $result['total'],
                'added' => $result['added'],
                'message' => $result['message']
            ];
        }
        
        return [
            'success' => true,
            'message' => '快速扫描完成',
            'total' => $totalFiles,
            'added' => $addedFiles,
            'scanned_dirs' => $scannedDirs,
            'config_dirs' => $scanDirs
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '快速扫描失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 快速扫描单个目录
 */
function quickScanSingleDir($directory, $fileManager, $allowedExtensions, $maxFiles = 50)
{
    $totalFiles = 0;
    $addedFiles = 0;
    
    if (!is_dir($directory)) {
        return [
            'exists' => false,
            'total' => 0,
            'added' => 0,
            'message' => '目录不存在'
        ];
    }
    
    try {
        $files = glob($directory . '/*');
        
        foreach ($files as $file) {
            if ($totalFiles >= $maxFiles) {
                break;
            }
            
            if (is_file($file)) {
                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                
                if (in_array($extension, $allowedExtensions)) {
                    $totalFiles++;
                    
                    // 简单检查文件是否已存在
                    if (!isFileInQuickDatabase($file, $fileManager)) {
                        if ($fileManager->addFile($file)) {
                            $addedFiles++;
                        }
                    }
                }
            }
        }
        
        return [
            'exists' => true,
            'total' => $totalFiles,
            'added' => $addedFiles,
            'message' => $totalFiles >= $maxFiles ? "扫描了前{$maxFiles}个文件" : "扫描完成"
        ];
        
    } catch (Exception $e) {
        return [
            'exists' => true,
            'total' => 0,
            'added' => 0,
            'message' => '扫描出错: ' . $e->getMessage()
        ];
    }
}

/**
 * 快速检查文件是否在数据库中
 */
function isFileInQuickDatabase($filePath, $fileManager)
{
    try {
        $db = $fileManager->getDatabase();
        $stmt = $db->prepare("SELECT id FROM qiniu_sync_files WHERE local_path = ? LIMIT 1");
        $stmt->execute([$filePath]);
        
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 测试扫描单个目录
 */
function testScanSingleDir($fileManager, $directory)
{
    if (empty($directory)) {
        return [
            'success' => false,
            'message' => '请指定目录路径'
        ];
    }
    
    if (!is_dir($directory)) {
        return [
            'success' => false,
            'message' => '目录不存在: ' . $directory
        ];
    }
    
    try {
        $files = glob($directory . '/*');
        $fileCount = 0;
        $dirCount = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $fileCount++;
            } elseif (is_dir($file)) {
                $dirCount++;
            }
        }
        
        return [
            'success' => true,
            'directory' => $directory,
            'file_count' => $fileCount,
            'dir_count' => $dirCount,
            'total_items' => count($files),
            'message' => "目录包含 {$fileCount} 个文件和 {$dirCount} 个子目录"
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '扫描失败: ' . $e->getMessage()
        ];
    }
}
?>
