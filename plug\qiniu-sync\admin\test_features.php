<?php
/**
 * 功能测试页面
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 设置会话（绕过登录验证）
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_username'] = 'test';

echo "<h2>功能测试页面</h2>";

try {
    $qiniuSync = new QiniuSync();
    echo "<p>✓ 插件实例创建成功</p>";
    
    // 测试文件管理器
    echo "<h3>文件管理器测试：</h3>";
    $fileManager = $qiniuSync->getFileManager();
    echo "<p>✓ 文件管理器获取成功</p>";
    
    $stats = $fileManager->getStats();
    echo "<p>文件统计: " . json_encode($stats) . "</p>";
    
    $files = $fileManager->getFiles(1, 5);
    echo "<p>文件列表 (前5个): " . count($files) . " 个文件</p>";
    
    // 测试队列管理器
    echo "<h3>队列管理器测试：</h3>";
    $queueManager = $qiniuSync->getQueueManager();
    echo "<p>✓ 队列管理器获取成功</p>";
    
    $queueStats = $queueManager->getQueueStats();
    echo "<p>队列统计: " . json_encode($queueStats) . "</p>";
    
    $tasks = $queueManager->getTasks(1, 5);
    echo "<p>任务列表 (前5个): " . count($tasks) . " 个任务</p>";
    
    // 测试日志记录器
    echo "<h3>日志记录器测试：</h3>";
    $logger = $qiniuSync->getLogger();
    echo "<p>✓ 日志记录器获取成功</p>";
    
    $logStats = $logger->getLogStatsByDate(date('Y-m-d'));
    echo "<p>今日日志统计: " . json_encode($logStats) . "</p>";
    
    $logs = $logger->getLogs(1, 5);
    echo "<p>日志列表 (前5个): " . count($logs) . " 条日志</p>";
    
    $availableDates = $logger->getAvailableDates();
    echo "<p>可用日期: " . implode(', ', array_slice($availableDates, 0, 5)) . "</p>";
    
    echo "<hr>";
    echo "<h3>功能页面链接：</h3>";
    echo "<ul>";
    echo "<li><a href='files.php' target='_blank'>文件管理</a></li>";
    echo "<li><a href='queue.php' target='_blank'>队列管理</a></li>";
    echo "<li><a href='logs.php' target='_blank'>日志查看</a></li>";
    echo "<li><a href='index.php' target='_blank'>管理面板</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>✗ 错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
