<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'MyCLabs\\Enum\\Enum' => $vendorDir . '/myclabs/php-enum/src/Enum.php',
    'MyCLabs\\Enum\\PHPUnit\\Comparator' => $vendorDir . '/myclabs/php-enum/src/PHPUnit/Comparator.php',
    '<PERSON>iu\\Auth' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Auth.php',
    'Qiniu\\Cdn\\CdnManager' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Cdn/CdnManager.php',
    'Qiniu\\Config' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Config.php',
    'Qiniu\\Enum\\QiniuEnum' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Enum/QiniuEnum.php',
    'Qiniu\\Enum\\SplitUploadVersion' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Enum/SplitUploadVersion.php',
    'Qiniu\\Etag' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Etag.php',
    'Qiniu\\Http\\Client' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/Client.php',
    'Qiniu\\Http\\Error' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/Error.php',
    'Qiniu\\Http\\Header' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/Header.php',
    'Qiniu\\Http\\Middleware\\Middleware' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/Middleware/Middleware.php',
    'Qiniu\\Http\\Middleware\\RetryDomainsMiddleware' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/Middleware/RetryDomainsMiddleware.php',
    'Qiniu\\Http\\Proxy' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/Proxy.php',
    'Qiniu\\Http\\Request' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/Request.php',
    'Qiniu\\Http\\RequestOptions' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/RequestOptions.php',
    'Qiniu\\Http\\Response' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Http/Response.php',
    'Qiniu\\Processing\\ImageUrlBuilder' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Processing/ImageUrlBuilder.php',
    'Qiniu\\Processing\\Operation' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Processing/Operation.php',
    'Qiniu\\Processing\\PersistentFop' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Processing/PersistentFop.php',
    'Qiniu\\Region' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Region.php',
    'Qiniu\\Rtc\\AppClient' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Rtc/AppClient.php',
    'Qiniu\\Sms\\Sms' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Sms/Sms.php',
    'Qiniu\\Storage\\ArgusManager' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Storage/ArgusManager.php',
    'Qiniu\\Storage\\BucketManager' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Storage/BucketManager.php',
    'Qiniu\\Storage\\FormUploader' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Storage/FormUploader.php',
    'Qiniu\\Storage\\ResumeUploader' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Storage/ResumeUploader.php',
    'Qiniu\\Storage\\UploadManager' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Storage/UploadManager.php',
    'Qiniu\\Zone' => $vendorDir . '/qiniu/php-sdk/src/Qiniu/Zone.php',
    'Stringable' => $vendorDir . '/myclabs/php-enum/stubs/Stringable.php',
);
