{"name": "maennchen/zipstream-php", "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["zip", "stream"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php-64bit": "^8.3", "ext-mbstring": "*", "ext-zlib": "*"}, "require-dev": {"phpunit/phpunit": "^12.0", "guzzlehttp/guzzle": "^7.5", "ext-zip": "*", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "friendsofphp/php-cs-fixer": "^3.16", "vimeo/psalm": "^6.0", "brianium/paratest": "^7.7"}, "suggest": {"psr/http-message": "^2.0", "guzzlehttp/psr7": "^2.4"}, "scripts": {"format": "php-cs-fixer fix", "test": ["@test:unit", "@test:formatted", "@test:lint"], "test:unit:setup-cov": "@putenv XDEBUG_MODE=coverage", "test:unit": "paratest --functional", "test:unit:cov": ["@test:unit:setup-cov", "@test:unit --coverage-clover=coverage.clover.xml --coverage-html cov"], "test:unit:slow": "@test:unit --group slow", "test:unit:slow:cov": ["@test:unit:setup-cov", "@test:unit --coverage-clover=coverage.clover.xml --coverage-html cov --group slow"], "test:unit:fast": "@test:unit --exclude-group slow", "test:unit:fast:cov": ["@test:unit:setup-cov", "@test:unit --coverage-clover=coverage.clover.xml --coverage-html cov --exclude-group slow"], "test:formatted": "@format --dry-run --stop-on-violation --using-cache=no", "test:lint": "psalm --stats --show-info=true --find-unused-psalm-suppress", "coverage:report": "php-coveralls --coverage_clover=coverage.clover.xml --json_path=coveralls-upload.json --insecure", "install:tools": "phive install --trust-gpg-keys 0x67F861C3D889C656 --trust-gpg-keys 0x8AC0BAA79732DD42 --trust-gpg-keys 0x6DA3ACC4991FFAE5", "docs:generate": "tools/phpdocumentor --sourcecode"}, "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "autoload-dev": {"psr-4": {"ZipStream\\Test\\": "test/"}}, "archive": {"exclude": ["/composer.lock", "/docs", "/.gitattributes", "/.github", "/.giti<PERSON>re", "/guides", "/.phive", "/.php-cs-fixer.cache", "/.php-cs-fixer.dist.php", "/.phpdoc", "/phpdoc.dist.xml", "/.phpunit.result.cache", "/phpunit.xml.dist", "/psalm.xml", "/test", "/tools", "/.tool-versions", "/vendor"]}}