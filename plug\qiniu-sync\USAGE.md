# 七牛云同步插件使用指南

## 快速开始

### 1. 安装插件

访问安装程序：
```
http://your-domain/plug/qiniu-sync/install.php
```

按照以下步骤操作：
1. 点击"检查依赖"确保环境满足要求
2. 点击"安装插件"创建数据库表
3. 点击"启用插件"激活功能

### 2. 配置七牛云

进入管理面板：
```
http://your-domain/plug/qiniu-sync/admin/index.php
```

在"配置设置"标签页中填写：
- **AccessKey**: 从七牛云控制台获取
- **SecretKey**: 从七牛云控制台获取  
- **Bucket**: 存储桶名称
- **Domain**: 绑定的访问域名
- **Prefix**: 文件前缀（可选）

点击"验证配置"确保配置正确。

### 3. 启动同步

在"同步控制"标签页中：
1. 点击"启动同步"开始监控文件变化
2. 可选择"全量同步"同步现有文件

## 功能说明

### 自动同步

插件会自动监控以下目录：
- `uploads/images/` （默认）

支持的文件格式：
- jpg, jpeg, png, gif, webp, bmp

当检测到文件变化时，会自动：
1. 计算文件哈希值避免重复上传
2. 添加到同步队列
3. 上传到七牛云存储
4. 生成访问URL
5. 记录同步日志

### 手动操作

#### 全量同步
```php
// 通过管理界面或API调用
$syncManager->syncAllFiles();
```

#### 单文件同步
```php
$result = $syncManager->uploadFile([
    'file_path' => 'uploads/images/example.jpg'
]);
```

#### 测试连接
```php
$result = $syncManager->testConnection();
```

### 队列管理

查看队列状态：
- 待处理任务数量
- 处理中任务
- 已完成任务
- 失败任务

重试失败任务：
```php
$queueManager->retryFailedJobs();
```

清空队列：
```php
$queueManager->clearQueue();
```

### 日志查看

插件提供详细的日志记录：
- 文件上传日志
- 错误信息记录
- 系统操作日志

日志级别：
- DEBUG: 调试信息
- INFO: 一般信息
- WARNING: 警告信息
- ERROR: 错误信息

## API 接口

### 获取插件状态
```php
$qiniuSync = new QiniuSync();
$status = $qiniuSync->getStatus();
```

### 配置管理
```php
$configManager = $qiniuSync->getConfigManager();

// 获取配置
$value = $configManager->getConfig('qiniu.bucket');

// 更新配置
$configManager->updateConfig('qiniu.bucket', 'my-bucket');

// 验证七牛云配置
$result = $configManager->validateQiniuConfig();
```

### 文件管理
```php
$fileManager = $qiniuSync->getFileManager();

// 扫描文件
$files = $fileManager->scanWatchPaths();

// 检查文件是否已同步
$synced = $fileManager->isFileSynced('uploads/images/test.jpg');

// 获取文件统计
$stats = $fileManager->getFileStats();
```

### 同步管理
```php
$syncManager = $qiniuSync->getSyncManager();

// 启动同步服务
$syncManager->start();

// 停止同步服务
$syncManager->stop();

// 手动同步所有文件
$result = $syncManager->syncAllFiles();

// 测试七牛云连接
$result = $syncManager->testConnection();
```

## 命令行工具

### 插件管理
```bash
# 安装插件
php plug/qiniu-sync/install.php install

# 启用插件
php plug/qiniu-sync/install.php enable

# 禁用插件
php plug/qiniu-sync/install.php disable

# 卸载插件
php plug/qiniu-sync/install.php uninstall

# 查看状态
php plug/qiniu-sync/install.php status
```

### 测试工具
```bash
# 运行完整测试
php plug/qiniu-sync/test.php

# 基础调试
php plug/qiniu-sync/debug.php
```

## 配置选项

### 七牛云配置
```php
$qiniuConfig = [
    'access_key' => 'your-access-key',
    'secret_key' => 'your-secret-key',
    'bucket' => 'your-bucket',
    'domain' => 'your-domain.com',
    'prefix' => 'images/',
    'region' => 'z0',
    'use_https' => true,
    'use_cdn_domain' => true
];
```

### 同步配置
```php
$syncConfig = [
    'enabled' => true,
    'watch_paths' => ['uploads/images/'],
    'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
    'max_file_size' => 10 * 1024 * 1024, // 10MB
    'auto_sync' => true,
    'batch_size' => 10,
    'retry_times' => 3
];
```

## 故障排除

### 常见问题

1. **上传失败**
   - 检查七牛云配置是否正确
   - 验证文件大小是否超限
   - 查看错误日志

2. **同步服务无法启动**
   - 确保插件已启用
   - 检查数据库连接
   - 验证七牛云配置

3. **文件重复上传**
   - 检查哈希校验是否启用
   - 清理无效的文件记录

### 日志位置
- 文件日志：`plug/qiniu-sync/logs/`
- 数据库日志：管理面板 -> 日志查看

### 性能优化
- 调整批处理大小
- 设置合适的并发数
- 定期清理过期日志

## 安全建议

1. **配置安全**
   - 使用强密码保护管理面板
   - 定期更换七牛云密钥
   - 启用配置加密

2. **文件安全**
   - 限制文件类型和大小
   - 设置合适的目录权限
   - 定期备份重要文件

3. **网络安全**
   - 使用HTTPS传输
   - 配置防火墙规则
   - 监控异常访问

## 更新和维护

### 版本更新
1. 备份现有配置和数据
2. 下载新版本文件
3. 运行更新脚本
4. 验证功能正常

### 定期维护
- 清理过期日志
- 检查存储空间使用
- 更新七牛云SDK
- 监控同步性能

## 技术支持

如遇问题，请提供以下信息：
- 插件版本
- PHP版本
- 错误日志
- 操作步骤

联系方式：
- 技术支持邮箱
- 在线文档
- 社区论坛
