<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试动态目录配置</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <h2>测试动态目录配置</h2>
        
        <?php
        session_start();
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = 'admin';
        
        // 引入插件核心文件
        require_once dirname(__DIR__) . '/bootstrap.php';
        
        try {
            $qiniuSync = new QiniuSync();
            $configManager = $qiniuSync->getConfigManager();
            
            // 获取当前配置的同步目录
            $currentDirs = $configManager->getConfig('sync.directories', []);
            $allowedExtensions = $configManager->getConfig('sync.allowed_extensions', []);
            
            echo "<div class='alert alert-success'>✓ 插件加载成功</div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>✗ 插件加载失败: " . htmlspecialchars($e->getMessage()) . "</div>";
            $currentDirs = [];
            $allowedExtensions = [];
        }
        ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>当前配置的同步目录</h5>
            </div>
            <div class="card-body">
                <?php if (empty($currentDirs)): ?>
                    <div class="alert alert-warning">
                        <strong>未配置同步目录</strong><br>
                        请先在管理面板中配置同步目录。
                        <a href="index.php" class="btn btn-primary btn-sm ms-2">去配置</a>
                    </div>
                <?php else: ?>
                    <h6>配置的目录列表:</h6>
                    <ul class="list-group mb-3">
                        <?php foreach ($currentDirs as $dir): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>
                                    <strong><?php echo htmlspecialchars($dir); ?></strong>
                                    <?php if (is_dir($dir)): ?>
                                        <span class="badge bg-success ms-2">存在</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger ms-2">不存在</span>
                                    <?php endif; ?>
                                </span>
                                <button class="btn btn-sm btn-outline-info" onclick="scanSingleDir('<?php echo htmlspecialchars($dir); ?>')">
                                    扫描此目录
                                </button>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    
                    <h6>允许的文件扩展名:</h6>
                    <p class="text-muted">
                        <?php echo empty($allowedExtensions) ? '未配置' : implode(', ', $allowedExtensions); ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>测试扫描功能</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <button class="btn btn-primary w-100 mb-2" onclick="testScanAllDirs()">
                            <i class="bi bi-search"></i> 扫描所有配置目录
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button class="btn btn-secondary w-100 mb-2" onclick="createTestFiles()">
                            <i class="bi bi-file-plus"></i> 创建测试文件
                        </button>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>扫描结果:</h6>
                    <div id="scan-results" style="background: #f8f9fa; padding: 15px; border-radius: 5px; min-height: 100px;">
                        点击上面的按钮开始测试...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>快速操作</h5>
            </div>
            <div class="card-body">
                <div class="d-flex gap-2">
                    <a href="index.php" class="btn btn-success">管理面板</a>
                    <a href="files.php" class="btn btn-primary">文件管理</a>
                    <button class="btn btn-info" onclick="refreshPage()">刷新页面</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResult(message, isSuccess = false) {
            const resultDiv = document.getElementById('scan-results');
            const timestamp = new Date().toLocaleTimeString();
            const color = isSuccess ? 'text-success' : 'text-danger';
            resultDiv.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function testScanAllDirs() {
            showResult('开始扫描所有配置的目录...', true);
            
            fetch('scan_files.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'scan_local_files'})
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showResult(`✅ 扫描完成！发现 ${data.total || 0} 个文件，添加 ${data.added || 0} 个新文件`, true);
                } else {
                    showResult('⚠️ 扫描失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                showResult('❌ 扫描请求失败: ' + error.message);
            });
        }

        function scanSingleDir(dir) {
            showResult(`开始扫描目录: ${dir}`, true);
            
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'scan_all_dirs'})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(`✅ 目录扫描完成！总计 ${data.total || 0} 个文件，添加 ${data.added || 0} 个`, true);
                } else {
                    showResult('⚠️ 目录扫描失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                showResult('❌ 目录扫描请求失败: ' + error.message);
            });
        }

        function createTestFiles() {
            showResult('创建测试文件...', true);
            
            fetch('ajax.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    action: 'save_sync_dirs',
                    sync_dirs: [
                        'D:/xampp/htdocs/BDLX/uploads/images',
                        'D:/xampp/htdocs/BDLX/test_files'
                    ],
                    allowed_extensions: ['jpg', 'jpeg', 'png', 'gif', 'txt'],
                    max_file_size: 10,
                    auto_scan: true,
                    delete_after_sync: false
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('✅ 测试目录配置已保存，请刷新页面查看', true);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showResult('⚠️ 配置保存失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                showResult('❌ 配置保存请求失败: ' + error.message);
            });
        }

        function refreshPage() {
            location.reload();
        }
    </script>
</body>
</html>
