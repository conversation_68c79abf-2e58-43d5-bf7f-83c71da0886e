<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <h2>AJAX请求测试</h2>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5>测试配置保存</h5>
                <button class="btn btn-primary" onclick="testSaveConfig()">测试保存配置</button>
                <button class="btn btn-secondary" onclick="testSaveDirs()">测试保存目录</button>
                <button class="btn btn-info" onclick="testValidateConfig()">测试验证配置</button>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>响应结果</h5>
            </div>
            <div class="card-body">
                <pre id="result" style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;"></pre>
            </div>
        </div>
    </div>

    <script>
        function showResult(data) {
            document.getElementById('result').textContent = JSON.stringify(data, null, 2);
        }

        function testSaveConfig() {
            const testData = {
                action: 'save_config',
                access_key: 'test_key',
                secret_key: 'test_secret',
                bucket: 'test_bucket',
                domain: 'test.domain.com'
            };

            fetch('ajax_debug.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    showResult(data);
                } catch (e) {
                    showResult({
                        error: 'JSON解析失败',
                        raw_response: text,
                        parse_error: e.message
                    });
                }
            })
            .catch(error => {
                showResult({
                    error: 'Fetch失败',
                    message: error.message
                });
            });
        }

        function testSaveDirs() {
            const testData = {
                action: 'save_sync_dirs',
                sync_dirs: ['/test/dir1', '/test/dir2'],
                allowed_extensions: ['jpg', 'png', 'gif'],
                max_file_size: 10
            };

            fetch('ajax_debug.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    showResult(data);
                } catch (e) {
                    showResult({
                        error: 'JSON解析失败',
                        raw_response: text,
                        parse_error: e.message
                    });
                }
            })
            .catch(error => {
                showResult({
                    error: 'Fetch失败',
                    message: error.message
                });
            });
        }

        function testValidateConfig() {
            const testData = {
                action: 'validate_config'
            };

            fetch('ajax_debug.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    showResult(data);
                } catch (e) {
                    showResult({
                        error: 'JSON解析失败',
                        raw_response: text,
                        parse_error: e.message
                    });
                }
            })
            .catch(error => {
                showResult({
                    error: 'Fetch失败',
                    message: error.message
                });
            });
        }
    </script>
</body>
</html>
