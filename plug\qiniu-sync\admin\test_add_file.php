<?php
/**
 * 测试添加文件功能
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

echo "<h2>测试添加文件功能</h2>";

try {
    $qiniuSync = new QiniuSync();
    echo "<p>✓ 插件实例创建成功</p>";
    
    $fileManager = $qiniuSync->getFileManager();
    echo "<p>✓ 文件管理器获取成功</p>";
    
    // 创建测试文件
    $testDir = dirname(__DIR__) . '/test_files';
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
        echo "<p>✓ 创建测试目录: $testDir</p>";
    }
    
    $testFile = $testDir . '/simple_test.txt';
    $content = 'Simple test file content - ' . date('Y-m-d H:i:s');
    file_put_contents($testFile, $content);
    echo "<p>✓ 创建测试文件: $testFile</p>";
    
    // 测试addFile方法
    echo "<h3>测试addFile方法:</h3>";
    
    // 检查方法是否存在
    if (method_exists($fileManager, 'addFile')) {
        echo "<p>✓ addFile方法存在</p>";
        
        try {
            $result = $fileManager->addFile($testFile);
            if ($result) {
                echo "<p>✓ 文件添加成功</p>";
            } else {
                echo "<p>✗ 文件添加失败</p>";
            }
        } catch (Exception $e) {
            echo "<p>✗ 添加文件时出错: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    } else {
        echo "<p>✗ addFile方法不存在</p>";
    }
    
    // 检查数据库中的文件
    echo "<h3>检查数据库:</h3>";
    try {
        $db = $qiniuSync->getConfigManager()->getDatabase();
        $stmt = $db->query("SELECT COUNT(*) as count FROM qiniu_sync_files");
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>数据库中的文件总数: " . $row['count'] . "</p>";
        
        $stmt = $db->prepare("SELECT * FROM qiniu_sync_files WHERE local_path LIKE ? ORDER BY created_at DESC LIMIT 5");
        $stmt->execute(['%test%']);
        $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($files)) {
            echo "<h4>最近的测试文件:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>本地路径</th><th>七牛云键</th><th>状态</th><th>创建时间</th></tr>";
            foreach ($files as $file) {
                echo "<tr>";
                echo "<td>" . $file['id'] . "</td>";
                echo "<td>" . htmlspecialchars($file['local_path']) . "</td>";
                echo "<td>" . htmlspecialchars($file['qiniu_key']) . "</td>";
                echo "<td>" . $file['sync_status'] . "</td>";
                echo "<td>" . $file['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>没有找到测试文件记录</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>✗ 数据库查询失败: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='add_test_files.php'>返回添加测试文件页面</a></p>";
    echo "<p><a href='index.php'>返回管理面板</a></p>";
    
} catch (Exception $e) {
    echo "<p>✗ 错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
