<?php
/**
 * 文件管理页面
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 检查权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

try {
    $qiniuSync = new QiniuSync();
    $fileManager = $qiniuSync->getFileManager();
    
    // 获取文件列表
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 20;
    $status = $_GET['status'] ?? 'all';
    $directory = $_GET['directory'] ?? '';

    $files = $fileManager->getFiles($page, $limit, $status, $directory);
    $totalFiles = $fileManager->getTotalFiles($status, $directory);
    $totalPages = ceil($totalFiles / $limit);

    // 获取统计信息
    $stats = $fileManager->getStats();

    // 获取配置的同步目录
    $configManager = $qiniuSync->getConfigManager();
    $syncDirs = $configManager->getConfig('sync.directories', []);
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $files = [];
    $totalFiles = 0;
    $totalPages = 0;
    $stats = [
        'total' => 0,
        'synced' => 0,
        'pending' => 0,
        'failed' => 0
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件管理 - 七牛云同步插件</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-cloud-upload"></i> 七牛云同步插件
            </a>
            <span class="navbar-text">文件管理</span>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> 错误: <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($stats['total']); ?></h5>
                        <p class="card-text">总文件数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center text-success">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($stats['synced']); ?></h5>
                        <p class="card-text">已同步</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center text-warning">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($stats['pending']); ?></h5>
                        <p class="card-text">待同步</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center text-danger">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($stats['failed']); ?></h5>
                        <p class="card-text">同步失败</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和操作 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex gap-2">
                            <select class="form-select" id="statusFilter" style="width: auto;">
                                <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>所有文件</option>
                                <option value="synced" <?php echo $status === 'synced' ? 'selected' : ''; ?>>已同步</option>
                                <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>待同步</option>
                                <option value="failed" <?php echo $status === 'failed' ? 'selected' : ''; ?>>同步失败</option>
                            </select>
                            <select class="form-select" id="directoryFilter" style="width: auto;">
                                <option value="">所有目录</option>
                                <?php foreach ($syncDirs as $dir): ?>
                                    <option value="<?php echo htmlspecialchars($dir); ?>"
                                            <?php echo $directory === $dir ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars(basename($dir) ?: $dir); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button class="btn btn-outline-primary" onclick="refreshFiles()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex gap-2 justify-content-end">
                            <div class="btn-group">
                                <button class="btn btn-info" onclick="quickScanFiles()">
                                    <i class="bi bi-lightning"></i> 快速扫描
                                </button>
                                <button class="btn btn-outline-info" onclick="scanLocalFiles()">
                                    <i class="bi bi-search"></i> 完整扫描
                                </button>
                            </div>
                            <button class="btn btn-success" onclick="syncAllFiles()">
                                <i class="bi bi-cloud-upload"></i> 同步所有文件
                            </button>
                            <button class="btn btn-warning" onclick="retryFailedFiles()">
                                <i class="bi bi-arrow-repeat"></i> 重试失败文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-files"></i> 文件列表
                    <small class="text-muted">(共 <?php echo number_format($totalFiles); ?> 个文件)</small>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($files)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <p class="text-muted mt-2">暂无文件</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>文件名</th>
                                    <th>大小</th>
                                    <th>状态</th>
                                    <th>七牛云URL</th>
                                    <th>最后更新</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($files as $file): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php
                                                $fileExists = file_exists($file['local_path']);
                                                $iconClass = $fileExists ? 'bi-file-earmark' : 'bi-file-earmark-x';
                                                $iconColor = $fileExists ? 'text-primary' : 'text-danger';
                                                ?>
                                                <i class="bi <?php echo $iconClass; ?> me-2 <?php echo $iconColor; ?>"></i>
                                                <div>
                                                    <div title="<?php echo htmlspecialchars($file['local_path']); ?>">
                                                        <strong><?php echo htmlspecialchars(basename($file['local_path'])); ?></strong>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php
                                                        $relativePath = str_replace(QINIU_SYNC_ROOT, '', dirname($file['local_path']));
                                                        echo htmlspecialchars(ltrim($relativePath, '/\\') ?: '/');
                                                        ?>
                                                    </small>
                                                    <?php if (!$fileExists): ?>
                                                        <br><small class="text-danger">⚠️ 文件不存在</small>
                                                    <?php else: ?>
                                                        <br><small class="text-success">✓ 文件存在</small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo formatFileSize($file['file_size'] ?? 0); ?></td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            switch ($file['sync_status']) {
                                                case 'synced':
                                                    $statusClass = 'success';
                                                    $statusText = '已同步';
                                                    break;
                                                case 'pending':
                                                    $statusClass = 'warning';
                                                    $statusText = '待同步';
                                                    break;
                                                case 'failed':
                                                    $statusClass = 'danger';
                                                    $statusText = '失败';
                                                    break;
                                                default:
                                                    $statusClass = 'secondary';
                                                    $statusText = '未知';
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td>
                                            <?php if (!empty($file['qiniu_url'])): ?>
                                                <a href="<?php echo htmlspecialchars($file['qiniu_url']); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-box-arrow-up-right"></i> 查看
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i:s', strtotime($file['updated_at'])); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($file['sync_status'] !== 'synced'): ?>
                                                    <button class="btn btn-outline-primary" onclick="syncFile(<?php echo $file['id']; ?>)">
                                                        <i class="bi bi-cloud-upload"></i>
                                                    </button>
                                                <?php endif; ?>
                                                <button class="btn btn-outline-danger" onclick="deleteFile(<?php echo $file['id']; ?>)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status; ?>&limit=<?php echo $limit; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 刷新文件列表
        function refreshFiles() {
            location.reload();
        }

        // 状态筛选
        document.getElementById('statusFilter').addEventListener('change', function() {
            const status = this.value;
            const directory = document.getElementById('directoryFilter').value;
            const url = new URL(window.location);
            url.searchParams.set('status', status);
            if (directory) {
                url.searchParams.set('directory', directory);
            } else {
                url.searchParams.delete('directory');
            }
            url.searchParams.set('page', '1');
            window.location = url;
        });

        // 目录筛选
        document.getElementById('directoryFilter').addEventListener('change', function() {
            const status = document.getElementById('statusFilter').value;
            const directory = this.value;
            const url = new URL(window.location);
            url.searchParams.set('status', status);
            if (directory) {
                url.searchParams.set('directory', directory);
            } else {
                url.searchParams.delete('directory');
            }
            url.searchParams.set('page', '1');
            window.location = url;
        });

        // 快速扫描本地文件
        function quickScanFiles() {
            if (confirm('快速扫描会限制每个目录最多50个文件，避免超时。确定要继续吗？')) {
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="bi bi-hourglass-split"></i> 扫描中...';
                button.disabled = true;

                fetch('quick_scan.php', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({action: 'quick_scan'})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`快速扫描完成！发现 ${data.total || 0} 个文件，添加 ${data.added || 0} 个新文件到同步队列。`);
                        location.reload();
                    } else {
                        alert('快速扫描失败: ' + (data.message || data.error));
                    }
                })
                .catch(error => {
                    alert('快速扫描请求失败: ' + error.message);
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }

        // 扫描本地文件（完整扫描）
        function scanLocalFiles() {
            if (confirm('完整扫描可能需要较长时间，如果文件很多可能会超时。建议先尝试快速扫描。确定要继续吗？')) {
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="bi bi-hourglass-split"></i> 扫描中...';
                button.disabled = true;

                fetch('scan_files.php', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({action: 'scan_local_files'})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`扫描完成！发现 ${data.total || 0} 个文件，添加 ${data.added || 0} 个新文件到同步队列。`);
                        location.reload();
                    } else {
                        alert('扫描失败：' + data.message);
                    }
                })
                .catch(error => alert('请求失败：' + error.message))
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
            }
        }

        // 同步所有文件
        function syncAllFiles() {
            if (confirm('确定要同步所有文件吗？这可能需要一些时间。')) {
                // 实现同步所有文件的逻辑
                alert('功能开发中...');
            }
        }

        // 重试失败文件
        function retryFailedFiles() {
            if (confirm('确定要重试所有失败的文件吗？')) {
                // 实现重试失败文件的逻辑
                alert('功能开发中...');
            }
        }

        // 同步单个文件
        function syncFile(fileId) {
            if (confirm('确定要同步这个文件吗？')) {
                // 实现同步单个文件的逻辑
                alert('功能开发中...');
            }
        }

        // 删除文件记录
        function deleteFile(fileId) {
            if (confirm('确定要删除这个文件记录吗？这不会删除本地文件。')) {
                // 实现删除文件记录的逻辑
                alert('功能开发中...');
            }
        }
    </script>
</body>
</html>

<?php
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}
?>
