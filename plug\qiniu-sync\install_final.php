<?php
/**
 * 七牛云同步插件最终安装程序
 */

// 引入插件核心文件
require_once __DIR__ . '/bootstrap.php';

// 获取插件状态
try {
    $qiniuSync = new QiniuSync();
    $status = $qiniuSync->getStatus();
    $pluginData = $status['data'];
    
    // 获取配置信息
    $configManager = $qiniuSync->getConfigManager();
    $qiniuConfig = [
        'access_key' => $configManager->getConfig('qiniu.access_key'),
        'secret_key' => $configManager->getConfig('qiniu.secret_key'),
        'bucket' => $configManager->getConfig('qiniu.bucket'),
        'domain' => $configManager->getConfig('qiniu.domain')
    ];
    
    // 检查配置完整性
    $configComplete = !empty($qiniuConfig['access_key']) && 
                     !empty($qiniuConfig['secret_key']) && 
                     !empty($qiniuConfig['bucket']) && 
                     !empty($qiniuConfig['domain']);
    
    // 验证配置
    $configValid = false;
    if ($configComplete) {
        $validation = $configManager->validateQiniuConfig();
        $configValid = $validation['valid'] ?? false;
    }
    
} catch (Exception $e) {
    $pluginData = [
        'installed' => false,
        'enabled' => false,
        'sync_running' => false
    ];
    $configComplete = false;
    $configValid = false;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>七牛云同步插件 - 安装完成</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .step-card {
            border-left: 4px solid #007bff;
        }
        .step-complete {
            border-left-color: #28a745;
        }
        .step-warning {
            border-left-color: #ffc107;
        }
        .step-error {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cloud-upload"></i> 七牛云同步插件 - 安装状态
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <!-- 总体状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-speedometer2"></i> 插件状态总览
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h2 mb-1">
                                        <span class="status-indicator <?php echo $pluginData['installed'] ? 'status-success' : 'status-error'; ?>"></span>
                                        <?php echo $pluginData['installed'] ? '已安装' : '未安装'; ?>
                                    </div>
                                    <small class="text-muted">插件安装</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h2 mb-1">
                                        <span class="status-indicator <?php echo $pluginData['enabled'] ? 'status-success' : 'status-error'; ?>"></span>
                                        <?php echo $pluginData['enabled'] ? '已启用' : '已禁用'; ?>
                                    </div>
                                    <small class="text-muted">插件状态</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h2 mb-1">
                                        <span class="status-indicator <?php echo $configComplete ? 'status-success' : 'status-error'; ?>"></span>
                                        <?php echo $configComplete ? '已配置' : '未配置'; ?>
                                    </div>
                                    <small class="text-muted">七牛云配置</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <div class="h2 mb-1">
                                        <span class="status-indicator <?php echo $pluginData['sync_running'] ? 'status-success' : 'status-error'; ?>"></span>
                                        <?php echo $pluginData['sync_running'] ? '运行中' : '已停止'; ?>
                                    </div>
                                    <small class="text-muted">同步服务</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安装步骤 -->
                <div class="row">
                    <!-- 步骤1：插件安装 -->
                    <div class="col-md-6 mb-4">
                        <div class="card step-card <?php echo $pluginData['installed'] ? 'step-complete' : 'step-error'; ?>">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-1-circle"></i> 插件安装
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if ($pluginData['installed']): ?>
                                    <div class="alert alert-success mb-2">
                                        <i class="bi bi-check-circle"></i> 插件已成功安装
                                    </div>
                                    <p class="mb-0">数据库表已创建，插件核心功能可用。</p>
                                <?php else: ?>
                                    <div class="alert alert-danger mb-2">
                                        <i class="bi bi-exclamation-triangle"></i> 插件未安装
                                    </div>
                                    <p class="mb-2">需要安装插件以创建数据库表。</p>
                                    <a href="install_debug.php" class="btn btn-primary btn-sm">
                                        <i class="bi bi-download"></i> 安装插件
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤2：插件启用 -->
                    <div class="col-md-6 mb-4">
                        <div class="card step-card <?php echo $pluginData['enabled'] ? 'step-complete' : ($pluginData['installed'] ? 'step-warning' : 'step-error'); ?>">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-2-circle"></i> 插件启用
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if ($pluginData['enabled']): ?>
                                    <div class="alert alert-success mb-2">
                                        <i class="bi bi-check-circle"></i> 插件已启用
                                    </div>
                                    <p class="mb-0">插件功能已激活，可以进行配置。</p>
                                <?php elseif ($pluginData['installed']): ?>
                                    <div class="alert alert-warning mb-2">
                                        <i class="bi bi-exclamation-triangle"></i> 插件未启用
                                    </div>
                                    <p class="mb-2">插件已安装但未启用。</p>
                                    <a href="install_debug.php" class="btn btn-warning btn-sm">
                                        <i class="bi bi-play"></i> 启用插件
                                    </a>
                                <?php else: ?>
                                    <div class="alert alert-secondary mb-2">
                                        <i class="bi bi-dash-circle"></i> 等待安装
                                    </div>
                                    <p class="mb-0">请先完成插件安装。</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤3：七牛云配置 -->
                    <div class="col-md-6 mb-4">
                        <div class="card step-card <?php echo $configValid ? 'step-complete' : ($configComplete ? 'step-warning' : 'step-error'); ?>">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-3-circle"></i> 七牛云配置
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if ($configValid): ?>
                                    <div class="alert alert-success mb-2">
                                        <i class="bi bi-check-circle"></i> 配置验证成功
                                    </div>
                                    <p class="mb-0">七牛云连接正常，可以开始同步。</p>
                                <?php elseif ($configComplete): ?>
                                    <div class="alert alert-warning mb-2">
                                        <i class="bi bi-exclamation-triangle"></i> 配置需要验证
                                    </div>
                                    <p class="mb-2">配置已填写但需要验证。</p>
                                    <a href="admin/fix_config.php" class="btn btn-warning btn-sm">
                                        <i class="bi bi-gear"></i> 验证配置
                                    </a>
                                <?php else: ?>
                                    <div class="alert alert-danger mb-2">
                                        <i class="bi bi-exclamation-triangle"></i> 配置未完成
                                    </div>
                                    <p class="mb-2">需要配置七牛云参数。</p>
                                    <a href="admin/fix_config.php" class="btn btn-primary btn-sm">
                                        <i class="bi bi-gear"></i> 配置七牛云
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤4：启动同步 -->
                    <div class="col-md-6 mb-4">
                        <div class="card step-card <?php echo $pluginData['sync_running'] ? 'step-complete' : ($configValid ? 'step-warning' : 'step-error'); ?>">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-4-circle"></i> 启动同步
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if ($pluginData['sync_running']): ?>
                                    <div class="alert alert-success mb-2">
                                        <i class="bi bi-check-circle"></i> 同步服务运行中
                                    </div>
                                    <p class="mb-0">文件同步功能已启动。</p>
                                <?php elseif ($configValid): ?>
                                    <div class="alert alert-warning mb-2">
                                        <i class="bi bi-exclamation-triangle"></i> 同步服务未启动
                                    </div>
                                    <p class="mb-2">配置正确，可以启动同步服务。</p>
                                    <a href="admin/index.php" class="btn btn-success btn-sm">
                                        <i class="bi bi-play"></i> 启动同步
                                    </a>
                                <?php else: ?>
                                    <div class="alert alert-secondary mb-2">
                                        <i class="bi bi-dash-circle"></i> 等待配置
                                    </div>
                                    <p class="mb-0">请先完成七牛云配置。</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning"></i> 快速操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2">
                            <a href="admin/index.php" class="btn btn-primary">
                                <i class="bi bi-speedometer2"></i> 管理面板
                            </a>
                            <a href="admin/fix_config.php" class="btn btn-outline-primary">
                                <i class="bi bi-gear"></i> 配置管理
                            </a>
                            <a href="install_debug.php" class="btn btn-outline-secondary">
                                <i class="bi bi-bug"></i> 调试工具
                            </a>
                            <a href="admin/test_direct.php" class="btn btn-outline-info" target="_blank">
                                <i class="bi bi-clipboard-check"></i> 状态检查
                            </a>
                            <a href="install_sdk.php" class="btn btn-outline-warning">
                                <i class="bi bi-download"></i> SDK管理
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
