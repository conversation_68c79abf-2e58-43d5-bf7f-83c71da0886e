<?php
/**
 * 七牛云同步插件简单安装程序
 */

// 引入插件核心文件
require_once __DIR__ . '/bootstrap.php';

// 检查插件状态
try {
    $qiniuSync = new QiniuSync();
    $status = $qiniuSync->getStatus();
    $pluginData = $status['data'];
} catch (Exception $e) {
    $pluginData = [
        'installed' => false,
        'enabled' => false,
        'sync_running' => false
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>七牛云同步插件 - 简单安装</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cloud-upload"></i> 七牛云同步插件 - 简单安装
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- 当前状态 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-speedometer2"></i> 当前状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p>
                                    <span class="status-indicator <?php echo $pluginData['installed'] ? 'status-success' : 'status-error'; ?>"></span>
                                    <strong>安装状态:</strong> <?php echo $pluginData['installed'] ? '已安装' : '未安装'; ?>
                                </p>
                                <p>
                                    <span class="status-indicator <?php echo $pluginData['enabled'] ? 'status-success' : 'status-error'; ?>"></span>
                                    <strong>启用状态:</strong> <?php echo $pluginData['enabled'] ? '已启用' : '已禁用'; ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p>
                                    <span class="status-indicator <?php echo $pluginData['sync_running'] ? 'status-success' : 'status-error'; ?>"></span>
                                    <strong>同步状态:</strong> <?php echo $pluginData['sync_running'] ? '运行中' : '已停止'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i> 快速操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!$pluginData['installed']): ?>
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle"></i> 插件未安装</h6>
                                <p>请先安装插件以创建必要的数据库表和配置。</p>
                                <a href="install_debug.php" class="btn btn-primary">
                                    <i class="bi bi-download"></i> 使用调试安装程序
                                </a>
                            </div>
                        <?php elseif (!$pluginData['enabled']): ?>
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle"></i> 插件已安装但未启用</h6>
                                <p>插件已安装但未启用，请启用插件后进行配置。</p>
                                <a href="install_debug.php" class="btn btn-success">
                                    <i class="bi bi-play"></i> 启用插件
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-success">
                                <h6><i class="bi bi-check-circle"></i> 插件已就绪</h6>
                                <p>插件已安装并启用，现在可以进行七牛云配置。</p>
                                <div class="d-flex gap-2">
                                    <a href="admin/index.php" class="btn btn-primary">
                                        <i class="bi bi-gear"></i> 进入管理面板
                                    </a>
                                    <a href="install_debug.php" class="btn btn-outline-secondary">
                                        <i class="bi bi-tools"></i> 调试工具
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 说明信息 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-question-circle"></i> 使用说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6>安装步骤：</h6>
                        <ol>
                            <li><strong>安装插件</strong> - 创建数据库表和初始配置</li>
                            <li><strong>启用插件</strong> - 激活插件功能</li>
                            <li><strong>配置七牛云</strong> - 在管理面板中设置七牛云参数</li>
                            <li><strong>启动同步</strong> - 开始自动同步文件</li>
                        </ol>

                        <h6 class="mt-3">七牛云配置参数：</h6>
                        <ul>
                            <li><strong>AccessKey</strong> - 七牛云访问密钥</li>
                            <li><strong>SecretKey</strong> - 七牛云私钥</li>
                            <li><strong>Bucket</strong> - 存储桶名称</li>
                            <li><strong>Domain</strong> - 绑定的访问域名</li>
                            <li><strong>Prefix</strong> - 文件存储前缀（可选）</li>
                        </ul>

                        <h6 class="mt-3">同步目录：</h6>
                        <p>默认监控 <code>uploads/images/</code> 目录下的图片文件。</p>

                        <h6 class="mt-3">支持格式：</h6>
                        <p>jpg, jpeg, png, gif, webp, bmp</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
