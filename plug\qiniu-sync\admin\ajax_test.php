<?php
/**
 * 七牛云同步插件AJAX测试处理器（无权限验证）
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不显示错误到页面，避免破坏JSON

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['action'])) {
        throw new Exception('无效的请求');
    }
    
    $action = $data['action'];
    $qiniuSync = new QiniuSync();
    
    switch ($action) {
        case 'validate_config':
            $result = $qiniuSync->getConfigManager()->validateQiniuConfig();
            // 确保返回格式正确
            if (!isset($result['valid'])) {
                $result = ['valid' => false, 'errors' => ['配置验证返回格式错误']];
            }
            break;
            
        case 'save_config':
            if (!isset($data['config'])) {
                throw new Exception('缺少配置数据');
            }
            $result = $qiniuSync->getConfigManager()->updateQiniuConfig($data['config']);
            $result = ['success' => $result, 'message' => $result ? '配置保存成功' : '配置保存失败'];
            break;
            
        case 'test_connection':
            $result = $qiniuSync->getSyncManager()->testConnection();
            break;
            
        case 'get_status':
            $result = $qiniuSync->getStatus();
            break;
            
        default:
            throw new Exception('未知操作: ' . $action);
    }
    
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'error' => 'PHP错误: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
