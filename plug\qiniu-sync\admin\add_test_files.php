<?php
/**
 * 添加测试文件到同步队列
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

echo "<h2>添加测试文件</h2>";

try {
    $qiniuSync = new QiniuSync();
    $fileManager = $qiniuSync->getFileManager();
    
    // 创建一些测试文件
    $testDir = dirname(__DIR__) . '/test_files';
    if (!is_dir($testDir)) {
        mkdir($testDir, 0755, true);
    }

    $testFiles = [
        'test1.txt' => 'This is test file 1 - ' . date('Y-m-d H:i:s'),
        'test2.txt' => 'This is test file 2 - ' . date('Y-m-d H:i:s'),
        'test3.txt' => 'This is test file 3 - ' . date('Y-m-d H:i:s')
    ];

    echo "<h3>创建测试文件:</h3>";
    foreach ($testFiles as $filename => $content) {
        $filePath = $testDir . '/' . $filename;
        file_put_contents($filePath, $content);
        echo "<p>✓ 创建文件: $filePath</p>";

        // 添加到文件管理器
        try {
            $result = $fileManager->addFile($filePath);
            if ($result) {
                echo "<p>✓ 添加到同步队列: $filename</p>";
            } else {
                echo "<p>✗ 添加失败: $filename</p>";
            }
        } catch (Exception $e) {
            echo "<p>✗ 添加失败: $filename - " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>当前文件状态:</h3>";
    $stats = $fileManager->getStats();
    echo "<p>总文件数: " . $stats['total'] . "</p>";
    echo "<p>待同步: " . $stats['pending'] . "</p>";
    echo "<p>已同步: " . $stats['synced'] . "</p>";
    echo "<p>失败: " . $stats['failed'] . "</p>";
    
    echo "<h3>待同步文件列表:</h3>";
    $pendingFiles = $fileManager->getPendingFiles(10);
    if (empty($pendingFiles)) {
        echo "<p>没有待同步的文件</p>";
    } else {
        echo "<ul>";
        foreach ($pendingFiles as $file) {
            echo "<li>" . htmlspecialchars($file['local_path']) . " (状态: " . $file['sync_status'] . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<hr>";
    echo "<p><a href='test_sync.php'>测试同步功能</a></p>";
    echo "<p><a href='index.php'>返回管理面板</a></p>";
    
} catch (Exception $e) {
    echo "<p>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
