<?php
/**
 * 七牛云同步插件队列管理器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class QiniuSyncQueueManager
{
    private $db;
    private $config;
    private $logger;
    
    /**
     * 构造函数
     */
    public function __construct($db, $config, $logger)
    {
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
    }
    
    /**
     * 添加任务到队列
     */
    public function addToQueue($filePath, $action = 'upload', $payload = [], $priority = 0)
    {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO qiniu_sync_queue 
                (file_path, action, priority, payload, scheduled_at) 
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                    action = VALUES(action),
                    priority = VALUES(priority),
                    payload = VALUES(payload),
                    status = 'pending',
                    attempts = 0,
                    scheduled_at = VALUES(scheduled_at),
                    updated_at = CURRENT_TIMESTAMP
            ");
            
            $payloadJson = !empty($payload) ? json_encode($payload, JSON_UNESCAPED_UNICODE) : null;
            $scheduledAt = date('Y-m-d H:i:s');
            
            $result = $stmt->execute([$filePath, $action, $priority, $payloadJson, $scheduledAt]);
            
            if ($result) {
                $jobId = $this->db->lastInsertId();
                $this->logger->info('任务添加到队列', [
                    'job_id' => $jobId,
                    'file_path' => $filePath,
                    'action' => $action,
                    'priority' => $priority
                ]);
                return $jobId;
            }
            
            return false;
        } catch (Exception $e) {
            $this->logger->error('添加任务到队列失败', [
                'file_path' => $filePath,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 获取下一批待处理任务
     */
    public function getNextJobs($limit = 10)
    {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM qiniu_sync_queue 
                WHERE status = 'pending' 
                    AND scheduled_at <= NOW()
                    AND attempts < max_attempts
                ORDER BY priority DESC, created_at ASC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->error('获取队列任务失败', ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 标记任务为处理中
     */
    public function markJobAsProcessing($jobId)
    {
        try {
            $stmt = $this->db->prepare("
                UPDATE qiniu_sync_queue 
                SET status = 'processing', 
                    started_at = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            return $stmt->execute([$jobId]);
        } catch (Exception $e) {
            $this->logger->error('标记任务为处理中失败', [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 标记任务为已完成
     */
    public function markJobAsCompleted($jobId)
    {
        try {
            $stmt = $this->db->prepare("
                UPDATE qiniu_sync_queue 
                SET status = 'completed', 
                    completed_at = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $result = $stmt->execute([$jobId]);
            
            if ($result) {
                $this->logger->info('任务完成', ['job_id' => $jobId]);
            }
            
            return $result;
        } catch (Exception $e) {
            $this->logger->error('标记任务为已完成失败', [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 标记任务为失败
     */
    public function markJobAsFailed($jobId, $errorMessage = null)
    {
        try {
            $stmt = $this->db->prepare("
                UPDATE qiniu_sync_queue 
                SET status = CASE 
                    WHEN attempts + 1 >= max_attempts THEN 'failed'
                    ELSE 'pending'
                END,
                attempts = attempts + 1,
                error_message = ?,
                scheduled_at = CASE 
                    WHEN attempts + 1 < max_attempts THEN DATE_ADD(NOW(), INTERVAL 60 SECOND)
                    ELSE scheduled_at
                END,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $result = $stmt->execute([$errorMessage, $jobId]);
            
            if ($result) {
                $this->logger->warning('任务失败', [
                    'job_id' => $jobId,
                    'error' => $errorMessage
                ]);
            }
            
            return $result;
        } catch (Exception $e) {
            $this->logger->error('标记任务为失败失败', [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 重试失败的任务
     */
    public function retryFailedJobs($jobIds = [])
    {
        try {
            $whereClause = "status = 'failed'";
            $params = [];
            
            if (!empty($jobIds)) {
                $placeholders = str_repeat('?,', count($jobIds) - 1) . '?';
                $whereClause .= " AND id IN ({$placeholders})";
                $params = $jobIds;
            }
            
            $stmt = $this->db->prepare("
                UPDATE qiniu_sync_queue 
                SET status = 'pending',
                    attempts = 0,
                    error_message = NULL,
                    scheduled_at = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP
                WHERE {$whereClause}
            ");
            
            $result = $stmt->execute($params);
            $affectedRows = $stmt->rowCount();
            
            $this->logger->info('重试失败任务', [
                'affected_rows' => $affectedRows,
                'job_ids' => $jobIds
            ]);
            
            return $affectedRows;
        } catch (Exception $e) {
            $this->logger->error('重试失败任务失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }
    
    /**
     * 获取队列大小
     */
    public function getQueueSize()
    {
        try {
            $stmt = $this->db->query("
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
                FROM qiniu_sync_queue
            ");
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->error('获取队列大小失败', ['error' => $e->getMessage()]);
            return [
                'total' => 0,
                'pending' => 0,
                'processing' => 0,
                'completed' => 0,
                'failed' => 0
            ];
        }
    }
    
    /**
     * 清空队列
     */
    public function clearQueue($status = null)
    {
        try {
            if ($status) {
                $stmt = $this->db->prepare("DELETE FROM qiniu_sync_queue WHERE status = ?");
                $result = $stmt->execute([$status]);
            } else {
                $stmt = $this->db->query("DELETE FROM qiniu_sync_queue");
                $result = $stmt !== false;
            }
            
            if ($result) {
                $deletedRows = $stmt->rowCount();
                $this->logger->info('清空队列', [
                    'status' => $status,
                    'deleted_rows' => $deletedRows
                ]);
                return $deletedRows;
            }
            
            return 0;
        } catch (Exception $e) {
            $this->logger->error('清空队列失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }
    
    /**
     * 获取队列任务列表
     */
    public function getQueueJobs($filters = [], $limit = 50, $offset = 0)
    {
        try {
            $where = [];
            $params = [];
            
            if (!empty($filters['status'])) {
                $where[] = 'status = ?';
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['action'])) {
                $where[] = 'action = ?';
                $params[] = $filters['action'];
            }
            
            if (!empty($filters['file_path'])) {
                $where[] = 'file_path LIKE ?';
                $params[] = '%' . $filters['file_path'] . '%';
            }
            
            if (!empty($filters['date_from'])) {
                $where[] = 'created_at >= ?';
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $where[] = 'created_at <= ?';
                $params[] = $filters['date_to'];
            }
            
            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
            
            $sql = "
                SELECT * FROM qiniu_sync_queue 
                {$whereClause}
                ORDER BY created_at DESC 
                LIMIT {$limit} OFFSET {$offset}
            ";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->error('获取队列任务列表失败', ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 删除已完成的任务
     */
    public function cleanupCompletedJobs($olderThanDays = 7)
    {
        try {
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$olderThanDays} days"));
            
            $stmt = $this->db->prepare("
                DELETE FROM qiniu_sync_queue 
                WHERE status = 'completed' 
                    AND completed_at < ?
            ");
            
            $result = $stmt->execute([$cutoffDate]);
            $deletedRows = $stmt->rowCount();
            
            $this->logger->info('清理已完成任务', [
                'deleted_rows' => $deletedRows,
                'cutoff_date' => $cutoffDate
            ]);
            
            return $deletedRows;
        } catch (Exception $e) {
            $this->logger->error('清理已完成任务失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }
    
    /**
     * 获取任务详情
     */
    public function getJobDetails($jobId)
    {
        try {
            $stmt = $this->db->prepare("SELECT * FROM qiniu_sync_queue WHERE id = ?");
            $stmt->execute([$jobId]);
            
            $job = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($job && !empty($job['payload'])) {
                $job['payload'] = json_decode($job['payload'], true);
            }
            
            return $job;
        } catch (Exception $e) {
            $this->logger->error('获取任务详情失败', [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 更新任务优先级
     */
    public function updateJobPriority($jobId, $priority)
    {
        try {
            $stmt = $this->db->prepare("
                UPDATE qiniu_sync_queue 
                SET priority = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            
            $result = $stmt->execute([$priority, $jobId]);
            
            if ($result) {
                $this->logger->info('更新任务优先级', [
                    'job_id' => $jobId,
                    'priority' => $priority
                ]);
            }
            
            return $result;
        } catch (Exception $e) {
            $this->logger->error('更新任务优先级失败', [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 取消任务
     */
    public function cancelJob($jobId)
    {
        try {
            $stmt = $this->db->prepare("
                UPDATE qiniu_sync_queue 
                SET status = 'failed', 
                    error_message = 'Job cancelled by user',
                    updated_at = CURRENT_TIMESTAMP 
                WHERE id = ? AND status IN ('pending', 'processing')
            ");
            
            $result = $stmt->execute([$jobId]);
            
            if ($result && $stmt->rowCount() > 0) {
                $this->logger->info('任务已取消', ['job_id' => $jobId]);
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            $this->logger->error('取消任务失败', [
                'job_id' => $jobId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 获取队列统计信息
     */
    public function getQueueStats($dateFrom = null, $dateTo = null)
    {
        try {
            $where = [];
            $params = [];
            
            if ($dateFrom) {
                $where[] = 'created_at >= ?';
                $params[] = $dateFrom;
            }
            
            if ($dateTo) {
                $where[] = 'created_at <= ?';
                $params[] = $dateTo;
            }
            
            $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
            
            $sql = "
                SELECT 
                    action,
                    status,
                    COUNT(*) as count,
                    AVG(TIMESTAMPDIFF(SECOND, started_at, completed_at)) as avg_processing_time
                FROM qiniu_sync_queue 
                {$whereClause}
                GROUP BY action, status
            ";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            $stats = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $stats[$row['action']][$row['status']] = [
                    'count' => $row['count'],
                    'avg_processing_time' => $row['avg_processing_time']
                ];
            }
            
            return $stats;
        } catch (Exception $e) {
            $this->logger->error('获取队列统计失败', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 获取任务列表
     */
    public function getTasks($page = 1, $limit = 20, $status = 'all')
    {
        try {
            $offset = ($page - 1) * $limit;

            if ($status === 'all') {
                $stmt = $this->db->prepare("
                    SELECT * FROM qiniu_sync_queue
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                ");
                $stmt->execute([$limit, $offset]);
            } else {
                $stmt = $this->db->prepare("
                    SELECT * FROM qiniu_sync_queue
                    WHERE status = ?
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                ");
                $stmt->execute([$status, $limit, $offset]);
            }

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 获取总任务数
     */
    public function getTotalTasks($status = 'all')
    {
        try {
            if ($status === 'all') {
                $stmt = $this->db->query("SELECT COUNT(*) as count FROM qiniu_sync_queue");
            } else {
                $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM qiniu_sync_queue WHERE status = ?");
                $stmt->execute([$status]);
            }
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            return $row['count'];
        } catch (Exception $e) {
            return 0;
        }
    }
}
