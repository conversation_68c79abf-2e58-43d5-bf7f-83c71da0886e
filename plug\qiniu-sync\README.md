# 七牛云同步插件

一个功能完整的七牛云对象存储自动同步插件，支持实时监控本地文件变化并自动同步到七牛云存储。

## 功能特性

### 🚀 核心功能
- **实时文件监控**: 自动监听 `uploads/images/` 目录下的文件变化
- **自动同步**: 新增、修改、删除文件时自动同步到七牛云
- **智能去重**: 基于文件哈希值避免重复上传
- **队列处理**: 支持批量处理和失败重试机制
- **多格式支持**: 支持 jpg, jpeg, png, gif, webp, bmp 等图片格式

### 🔧 配置管理
- **安全存储**: 敏感配置信息加密存储
- **配置验证**: 实时验证七牛云配置的有效性
- **灵活配置**: 支持自定义监控路径、文件类型等
- **区域选择**: 支持七牛云各个存储区域

### 📊 管理界面
- **状态监控**: 实时显示同步状态和统计信息
- **文件管理**: 查看文件同步记录和状态
- **队列管理**: 监控和管理同步队列
- **日志查看**: 详细的操作日志和错误记录

### ⚡ 性能优化
- **并发上传**: 支持多文件并发上传
- **批量处理**: 批量处理队列任务
- **内存优化**: 合理的内存使用限制
- **错误重试**: 智能的失败重试机制

## 系统要求

- **PHP**: >= 7.2
- **扩展**: curl, json, mbstring, openssl, pdo, pdo_mysql
- **数据库**: MySQL 5.7+ 或 MariaDB 10.2+
- **七牛云账号**: 需要有效的 AccessKey 和 SecretKey

## 安装步骤

### 1. 安装依赖

插件已自动添加到项目的 `composer.json` 中，七牛云 SDK 会自动安装。

### 2. 访问安装程序

在浏览器中访问：
```
http://your-domain/plug/qiniu-sync/install.php
```

### 3. 检查依赖

点击"检查依赖"按钮，确保所有依赖都满足要求。

### 4. 安装插件

点击"安装插件"按钮，系统会自动：
- 创建数据库表
- 初始化配置
- 设置必要的目录权限

### 5. 配置七牛云

进入管理面板，配置七牛云参数：
- **AccessKey**: 七牛云访问密钥
- **SecretKey**: 七牛云私钥
- **Bucket**: 目标存储桶名称
- **Domain**: 七牛云绑定的访问域名
- **Prefix**: 文件存储前缀（可选）
- **Region**: 存储区域

### 6. 启用插件

配置完成后，启用插件开始使用。

## 使用方法

### 管理界面

访问管理面板：
```
http://your-domain/plug/qiniu-sync/admin/index.php
```

管理界面包含以下功能模块：

#### 仪表板
- 显示插件状态和统计信息
- 测试七牛云连接
- 查看同步概览

#### 配置设置
- 七牛云参数配置
- 同步路径设置
- 文件类型限制

#### 同步控制
- 启动/停止同步服务
- 手动全量同步
- 清理失效文件

#### 文件管理
- 查看文件同步记录
- 文件状态监控
- 访问七牛云文件

#### 队列管理
- 查看同步队列
- 重试失败任务
- 清理队列

#### 日志查看
- 操作日志记录
- 错误信息追踪
- 日志级别过滤

### 命令行操作

插件支持命令行管理：

```bash
# 安装插件
php plug/qiniu-sync/install.php install

# 启用插件
php plug/qiniu-sync/install.php enable

# 禁用插件
php plug/qiniu-sync/install.php disable

# 卸载插件
php plug/qiniu-sync/install.php uninstall

# 查看状态
php plug/qiniu-sync/install.php status

# 测试依赖
php plug/qiniu-sync/install.php test
```

## 配置说明

### 七牛云配置

| 参数 | 说明 | 必填 |
|------|------|------|
| AccessKey | 七牛云访问密钥 | 是 |
| SecretKey | 七牛云私钥 | 是 |
| Bucket | 存储桶名称 | 是 |
| Domain | 访问域名 | 是 |
| Prefix | 文件前缀 | 否 |
| Region | 存储区域 | 否 |

### 同步配置

| 参数 | 说明 | 默认值 |
|------|------|--------|
| watch_paths | 监控路径 | ["uploads/images/"] |
| allowed_extensions | 允许的文件扩展名 | ["jpg","jpeg","png","gif","webp","bmp"] |
| max_file_size | 最大文件大小 | 10MB |
| batch_size | 批处理大小 | 10 |
| retry_times | 重试次数 | 3 |

## 数据库表结构

插件会创建以下数据库表：

- `qiniu_sync_config`: 配置信息
- `qiniu_sync_files`: 文件记录
- `qiniu_sync_logs`: 操作日志
- `qiniu_sync_queue`: 同步队列
- `qiniu_sync_stats`: 统计信息

## 安全考虑

- **配置加密**: 敏感配置信息使用 AES-256-CBC 加密
- **权限控制**: 需要管理员权限才能访问
- **文件验证**: 严格的文件类型和大小限制
- **错误处理**: 完善的错误处理和日志记录

## 故障排除

### 常见问题

1. **依赖检查失败**
   - 确保 PHP 版本 >= 7.2
   - 安装必要的 PHP 扩展
   - 检查数据库连接

2. **七牛云连接失败**
   - 验证 AccessKey 和 SecretKey
   - 检查 Bucket 是否存在
   - 确认网络连接正常

3. **文件上传失败**
   - 检查文件大小限制
   - 验证文件类型是否支持
   - 查看错误日志

4. **同步服务无法启动**
   - 检查插件是否已启用
   - 验证七牛云配置
   - 查看系统日志

### 日志位置

- **文件日志**: `plug/qiniu-sync/logs/`
- **数据库日志**: `qiniu_sync_logs` 表

## 版本信息

- **当前版本**: 1.0.0
- **作者**: BDLX Team
- **许可证**: MIT

## 更新日志

### v1.0.0 (2025-01-05)
- 初始版本发布
- 实现基础同步功能
- 添加管理界面
- 支持队列处理
- 完善日志记录

## 技术支持

如有问题或建议，请联系技术支持团队。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个插件。
