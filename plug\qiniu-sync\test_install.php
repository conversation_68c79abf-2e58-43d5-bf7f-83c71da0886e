<?php
/**
 * 七牛云同步插件安装测试页面
 */

// 设置执行时间限制
set_time_limit(60);
ini_set('memory_limit', '256M');

// 引入插件核心文件
require_once __DIR__ . '/bootstrap.php';

echo "<h1>七牛云同步插件安装测试</h1>";

// 测试数据库连接
echo "<h2>1. 数据库连接测试</h2>";
try {
    $dbConfigFile = QINIU_SYNC_PATH . '/../../config.php';
    if (file_exists($dbConfigFile)) {
        include $dbConfigFile;
        
        if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            echo "<p style='color: green;'>✓ 数据库连接成功</p>";
        } else {
            echo "<p style='color: red;'>✗ 数据库配置常量未定义</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ 数据库配置文件不存在</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
}

// 测试七牛云SDK
echo "<h2>2. 七牛云SDK测试</h2>";
if (class_exists('Qiniu\Auth')) {
    echo "<p style='color: green;'>✓ 七牛云SDK已加载</p>";
} else {
    echo "<p style='color: red;'>✗ 七牛云SDK未加载</p>";
}

// 测试插件类
echo "<h2>3. 插件类测试</h2>";
try {
    $qiniuSync = new QiniuSync();
    echo "<p style='color: green;'>✓ QiniuSync 类实例化成功</p>";
    
    // 测试状态获取
    $status = $qiniuSync->getStatus();
    if ($status['success']) {
        echo "<p style='color: green;'>✓ 状态获取成功</p>";
        echo "<pre>" . print_r($status['data'], true) . "</pre>";
    } else {
        echo "<p style='color: orange;'>⚠ 状态获取失败: " . $status['message'] . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 插件类测试失败: " . $e->getMessage() . "</p>";
}

// 测试依赖检查
echo "<h2>4. 依赖检查测试</h2>";
try {
    $result = testDependencies();
    if ($result['success']) {
        echo "<p style='color: green;'>✓ 所有依赖检查通过</p>";
    } else {
        echo "<p style='color: orange;'>⚠ 存在依赖问题</p>";
        echo "<pre>" . print_r($result['details'], true) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 依赖检查失败: " . $e->getMessage() . "</p>";
}

// 测试安装功能
echo "<h2>5. 安装功能测试</h2>";
try {
    $qiniuSync = new QiniuSync();
    $installResult = $qiniuSync->install();
    
    if ($installResult['success']) {
        echo "<p style='color: green;'>✓ 插件安装成功</p>";
    } else {
        echo "<p style='color: red;'>✗ 插件安装失败: " . $installResult['message'] . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 安装测试失败: " . $e->getMessage() . "</p>";
}

echo "<h2>测试完成</h2>";
echo "<p><a href='install.php'>返回安装页面</a></p>";

/**
 * 测试依赖函数（从 install.php 复制）
 */
function testDependencies()
{
    $results = [];
    
    // 检查PHP版本
    $phpVersion = PHP_VERSION;
    $results['php_version'] = [
        'required' => '7.2.0',
        'current' => $phpVersion,
        'status' => version_compare($phpVersion, '7.2.0', '>=')
    ];
    
    // 检查必要扩展
    $requiredExtensions = ['curl', 'json', 'mbstring', 'openssl', 'pdo', 'pdo_mysql'];
    foreach ($requiredExtensions as $ext) {
        $results['extensions'][$ext] = [
            'required' => true,
            'status' => extension_loaded($ext)
        ];
    }
    
    // 检查七牛云SDK
    $results['qiniu_sdk'] = [
        'required' => true,
        'status' => class_exists('Qiniu\Auth')
    ];
    
    // 检查目录权限
    $directories = [
        QINIU_SYNC_PATH . '/logs',
        QINIU_SYNC_PATH . '/cache',
        QINIU_SYNC_PATH . '/temp'
    ];
    
    foreach ($directories as $dir) {
        $results['directories'][basename($dir)] = [
            'path' => $dir,
            'exists' => is_dir($dir),
            'writable' => is_writable($dir)
        ];
    }
    
    // 检查数据库连接
    try {
        $dbConfigFile = QINIU_SYNC_PATH . '/../../config.php';
        if (file_exists($dbConfigFile)) {
            include $dbConfigFile;
            
            if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
                $pdo = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                    DB_USER,
                    DB_PASS,
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
                $results['database'] = [
                    'status' => true,
                    'message' => '数据库连接正常'
                ];
            } else {
                $results['database'] = [
                    'status' => false,
                    'message' => '数据库配置常量未定义'
                ];
            }
        } else {
            $results['database'] = [
                'status' => false,
                'message' => '数据库配置文件不存在'
            ];
        }
    } catch (Exception $e) {
        $results['database'] = [
            'status' => false,
            'message' => '数据库连接失败: ' . $e->getMessage()
        ];
    }
    
    // 计算总体状态
    $allPassed = true;
    
    if (!$results['php_version']['status']) {
        $allPassed = false;
    }
    
    foreach ($results['extensions'] as $ext => $info) {
        if (!$info['status']) {
            $allPassed = false;
            break;
        }
    }
    
    if (!$results['qiniu_sdk']['status']) {
        $allPassed = false;
    }
    
    if (!$results['database']['status']) {
        $allPassed = false;
    }
    
    return [
        'success' => $allPassed,
        'message' => $allPassed ? '所有依赖检查通过' : '存在依赖问题',
        'details' => $results
    ];
}
?>
