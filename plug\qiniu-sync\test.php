<?php
/**
 * 七牛云同步插件测试脚本
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 引入插件核心文件
require_once __DIR__ . '/bootstrap.php';

echo "七牛云同步插件测试脚本\n";
echo "========================\n\n";

// 测试1: 检查依赖
echo "1. 检查依赖...\n";
$dependencyErrors = [];

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.2.0', '<')) {
    $dependencyErrors[] = "PHP版本过低，需要 >= 7.2.0，当前: " . PHP_VERSION;
} else {
    echo "   ✓ PHP版本: " . PHP_VERSION . "\n";
}

// 检查必要扩展
$requiredExtensions = ['curl', 'json', 'mbstring', 'openssl', 'pdo', 'pdo_mysql'];
foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $dependencyErrors[] = "缺少PHP扩展: {$ext}";
    } else {
        echo "   ✓ PHP扩展: {$ext}\n";
    }
}

// 检查七牛云SDK
if (!class_exists('Qiniu\Auth')) {
    $dependencyErrors[] = "七牛云SDK未安装";
} else {
    echo "   ✓ 七牛云SDK已安装\n";
}

if (!empty($dependencyErrors)) {
    echo "\n❌ 依赖检查失败:\n";
    foreach ($dependencyErrors as $error) {
        echo "   - {$error}\n";
    }
    exit(1);
}

echo "   ✅ 所有依赖检查通过\n\n";

// 测试2: 检查目录权限
echo "2. 检查目录权限...\n";
$directories = [
    QINIU_SYNC_PATH . '/logs',
    QINIU_SYNC_PATH . '/cache',
    QINIU_SYNC_PATH . '/temp'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0755, true)) {
            echo "   ❌ 无法创建目录: {$dir}\n";
            exit(1);
        }
    }
    
    if (!is_writable($dir)) {
        echo "   ❌ 目录不可写: {$dir}\n";
        exit(1);
    }
    
    echo "   ✓ 目录权限正常: {$dir}\n";
}

echo "   ✅ 目录权限检查通过\n\n";

// 测试3: 测试数据库连接
echo "3. 测试数据库连接...\n";
try {
    $dbConfigFile = QINIU_SYNC_PATH . '/../../config.php';
    if (!file_exists($dbConfigFile)) {
        echo "   ❌ 数据库配置文件不存在: {$dbConfigFile}\n";
        exit(1);
    }

    include_once $dbConfigFile;

    // 检查数据库配置常量
    if (!defined('DB_HOST') || !defined('DB_NAME') || !defined('DB_USER')) {
        echo "   ❌ 数据库配置常量未定义\n";
        exit(1);
    }

    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );

    echo "   ✓ 数据库连接成功\n";
    echo "   ✅ 数据库连接测试通过\n\n";
} catch (Exception $e) {
    echo "   ❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 测试4: 测试插件类加载
echo "4. 测试插件类加载...\n";
try {
    $qiniuSync = new QiniuSync();
    echo "   ✓ QiniuSync 主类加载成功\n";
    
    $configManager = $qiniuSync->getConfigManager();
    echo "   ✓ ConfigManager 加载成功\n";
    
    $fileManager = $qiniuSync->getFileManager();
    echo "   ✓ FileManager 加载成功\n";
    
    $syncManager = $qiniuSync->getSyncManager();
    echo "   ✓ SyncManager 加载成功\n";
    
    $queueManager = $qiniuSync->getQueueManager();
    echo "   ✓ QueueManager 加载成功\n";
    
    $logger = $qiniuSync->getLogger();
    echo "   ✓ Logger 加载成功\n";
    
    echo "   ✅ 所有插件类加载成功\n\n";
} catch (Exception $e) {
    echo "   ❌ 插件类加载失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 测试5: 测试配置管理
echo "5. 测试配置管理...\n";
try {
    // 测试配置读取
    $testConfig = $configManager->getConfig('plugin.version', 'unknown');
    echo "   ✓ 配置读取测试: plugin.version = {$testConfig}\n";
    
    // 测试配置写入
    $testKey = 'test.timestamp';
    $testValue = time();
    if ($configManager->updateConfig($testKey, $testValue)) {
        echo "   ✓ 配置写入测试成功\n";
        
        // 验证写入的配置
        $readValue = $configManager->getConfig($testKey);
        if ($readValue == $testValue) {
            echo "   ✓ 配置读取验证成功\n";
        } else {
            echo "   ❌ 配置读取验证失败\n";
        }
        
        // 清理测试配置
        $configManager->deleteConfig($testKey);
        echo "   ✓ 测试配置清理完成\n";
    } else {
        echo "   ❌ 配置写入测试失败\n";
    }
    
    echo "   ✅ 配置管理测试通过\n\n";
} catch (Exception $e) {
    echo "   ❌ 配置管理测试失败: " . $e->getMessage() . "\n";
}

// 测试6: 测试日志功能
echo "6. 测试日志功能...\n";
try {
    $logger->info('测试日志记录', ['test' => true, 'timestamp' => time()]);
    echo "   ✓ 日志写入测试成功\n";
    
    $logger->warning('测试警告日志');
    echo "   ✓ 警告日志测试成功\n";
    
    $logger->error('测试错误日志');
    echo "   ✓ 错误日志测试成功\n";
    
    // 检查日志文件
    $logFiles = $logger->getLogFiles();
    if (!empty($logFiles)) {
        echo "   ✓ 日志文件创建成功，共 " . count($logFiles) . " 个文件\n";
    }
    
    echo "   ✅ 日志功能测试通过\n\n";
} catch (Exception $e) {
    echo "   ❌ 日志功能测试失败: " . $e->getMessage() . "\n";
}

// 测试7: 测试文件管理
echo "7. 测试文件管理...\n";
try {
    // 测试文件哈希计算
    $testFile = __FILE__;
    $hash = $fileManager->calculateFileHash($testFile);
    if ($hash) {
        echo "   ✓ 文件哈希计算成功: " . substr($hash, 0, 16) . "...\n";
    }
    
    // 测试MIME类型检测
    $mimeType = $fileManager->getMimeType($testFile);
    if ($mimeType) {
        echo "   ✓ MIME类型检测成功: {$mimeType}\n";
    }
    
    // 测试文件统计
    $stats = $fileManager->getFileStats();
    echo "   ✓ 文件统计获取成功: 总文件 " . ($stats['total_files'] ?? 0) . " 个\n";
    
    echo "   ✅ 文件管理测试通过\n\n";
} catch (Exception $e) {
    echo "   ❌ 文件管理测试失败: " . $e->getMessage() . "\n";
}

// 测试8: 测试队列管理
echo "8. 测试队列管理...\n";
try {
    // 测试队列大小获取
    $queueSize = $queueManager->getQueueSize();
    echo "   ✓ 队列大小获取成功: " . json_encode($queueSize) . "\n";
    
    // 测试添加队列任务
    $jobId = $queueManager->addToQueue('test/file.jpg', 'upload', ['test' => true]);
    if ($jobId) {
        echo "   ✓ 队列任务添加成功: Job ID {$jobId}\n";
        
        // 清理测试任务
        $queueManager->cancelJob($jobId);
        echo "   ✓ 测试任务清理完成\n";
    }
    
    echo "   ✅ 队列管理测试通过\n\n";
} catch (Exception $e) {
    echo "   ❌ 队列管理测试失败: " . $e->getMessage() . "\n";
}

// 测试9: 测试插件状态
echo "9. 测试插件状态...\n";
try {
    $status = $qiniuSync->getStatus();
    if ($status['success']) {
        echo "   ✓ 插件状态获取成功\n";
        $data = $status['data'];
        echo "   - 已安装: " . ($data['installed'] ? '是' : '否') . "\n";
        echo "   - 已启用: " . ($data['enabled'] ? '是' : '否') . "\n";
        echo "   - 同步运行: " . ($data['sync_running'] ? '是' : '否') . "\n";
        echo "   - 队列大小: " . ($data['queue_size']['total'] ?? 0) . "\n";
    } else {
        echo "   ❌ 插件状态获取失败: " . $status['message'] . "\n";
    }
    
    echo "   ✅ 插件状态测试通过\n\n";
} catch (Exception $e) {
    echo "   ❌ 插件状态测试失败: " . $e->getMessage() . "\n";
}

// 测试总结
echo "🎉 所有测试完成！\n";
echo "========================\n";
echo "插件基础功能测试通过，可以进行安装和配置。\n\n";

echo "下一步操作:\n";
echo "1. 访问安装程序: http://your-domain/plug/qiniu-sync/install.php\n";
echo "2. 安装插件并配置七牛云参数\n";
echo "3. 访问管理面板: http://your-domain/plug/qiniu-sync/admin/index.php\n";
echo "4. 启用插件开始使用\n\n";

echo "注意事项:\n";
echo "- 确保已正确配置七牛云 AccessKey 和 SecretKey\n";
echo "- 检查 uploads/images/ 目录的读写权限\n";
echo "- 建议先在测试环境中验证功能\n";
?>
