<?php
/**
 * 七牛云同步插件文件管理器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

class QiniuSyncFileManager
{
    private $db;
    private $config;
    private $logger;
    private $configManager;
    private $watchPaths;
    private $allowedExtensions;
    private $maxFileSize;
    
    /**
     * 构造函数
     */
    public function __construct($db, $config, $logger)
    {
        $this->db = $db;
        $this->config = $config;
        $this->logger = $logger;
        $this->initializeConfig();
    }
    
    /**
     * 初始化配置
     */
    private function initializeConfig()
    {
        $this->configManager = new QiniuSyncConfigManager($this->db, $this->config, $this->logger);

        $this->watchPaths = $this->configManager->getConfig('sync.watch_paths', ['uploads/images/']);
        $this->allowedExtensions = $this->configManager->getConfig('sync.allowed_extensions', ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']);
        $this->maxFileSize = $this->configManager->getConfig('sync.max_file_size', 10 * 1024 * 1024);
    }
    
    /**
     * 扫描监控目录中的文件
     */
    public function scanWatchPaths()
    {
        $files = [];
        
        foreach ($this->watchPaths as $path) {
            $fullPath = QINIU_SYNC_ROOT . '/' . ltrim($path, '/');
            
            if (!is_dir($fullPath)) {
                $this->logger->warning('监控目录不存在', ['path' => $fullPath]);
                continue;
            }
            
            $pathFiles = $this->scanDirectory($fullPath, $path);
            $files = array_merge($files, $pathFiles);
        }
        
        return $files;
    }
    
    /**
     * 扫描目录
     */
    private function scanDirectory($fullPath, $relativePath)
    {
        $files = [];
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($fullPath, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $this->isValidFile($file)) {
                $relativeFilePath = $relativePath . substr($file->getPathname(), strlen($fullPath));
                $relativeFilePath = str_replace('\\', '/', $relativeFilePath);
                
                $files[] = [
                    'local_path' => $relativeFilePath,
                    'full_path' => $file->getPathname(),
                    'size' => $file->getSize(),
                    'modified' => $file->getMTime(),
                    'hash' => $this->calculateFileHash($file->getPathname())
                ];
            }
        }
        
        return $files;
    }
    
    /**
     * 检查文件是否有效
     */
    private function isValidFile($file)
    {
        // 检查文件扩展名
        $extension = strtolower($file->getExtension());
        if (!in_array($extension, $this->allowedExtensions)) {
            return false;
        }
        
        // 检查文件大小
        if ($file->getSize() > $this->maxFileSize) {
            $this->logger->warning('文件超过大小限制', [
                'file' => $file->getPathname(),
                'size' => $file->getSize(),
                'limit' => $this->maxFileSize
            ]);
            return false;
        }
        
        // 检查文件是否可读
        if (!$file->isReadable()) {
            $this->logger->warning('文件不可读', ['file' => $file->getPathname()]);
            return false;
        }
        
        return true;
    }
    
    /**
     * 计算文件哈希值
     */
    public function calculateFileHash($filePath)
    {
        if (!file_exists($filePath)) {
            return null;
        }
        
        return hash_file('sha256', $filePath);
    }
    
    /**
     * 检查文件是否已同步
     */
    public function isFileSynced($localPath)
    {
        try {
            $stmt = $this->db->prepare("
                SELECT id, file_hash, sync_status 
                FROM qiniu_sync_files 
                WHERE local_path = ?
            ");
            $stmt->execute([$localPath]);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$row) {
                return false;
            }
            
            // 检查文件是否已成功同步
            if ($row['sync_status'] !== 'success') {
                return false;
            }
            
            // 检查文件是否有变化
            $fullPath = QINIU_SYNC_ROOT . '/' . ltrim($localPath, '/');
            $currentHash = $this->calculateFileHash($fullPath);
            
            return $currentHash === $row['file_hash'];
        } catch (Exception $e) {
            $this->logger->error('检查文件同步状态失败', [
                'file' => $localPath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 记录文件信息
     */
    public function recordFile($localPath, $qiniuKey, $qiniuUrl = null, $fileHash = null, $fileSize = null, $mimeType = null)
    {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO qiniu_sync_files 
                (local_path, qiniu_key, qiniu_url, file_hash, file_size, mime_type, sync_status) 
                VALUES (?, ?, ?, ?, ?, ?, 'pending')
                ON DUPLICATE KEY UPDATE 
                    qiniu_key = VALUES(qiniu_key),
                    qiniu_url = VALUES(qiniu_url),
                    file_hash = VALUES(file_hash),
                    file_size = VALUES(file_size),
                    mime_type = VALUES(mime_type),
                    sync_status = 'pending',
                    sync_attempts = 0,
                    updated_at = CURRENT_TIMESTAMP
            ");
            
            $result = $stmt->execute([$localPath, $qiniuKey, $qiniuUrl, $fileHash, $fileSize, $mimeType]);
            
            if ($result) {
                $fileId = $this->db->lastInsertId() ?: $this->getFileId($localPath);
                $this->logger->info('文件记录成功', [
                    'file_id' => $fileId,
                    'local_path' => $localPath,
                    'qiniu_key' => $qiniuKey
                ]);
                return $fileId;
            }
            
            return false;
        } catch (Exception $e) {
            $this->logger->error('记录文件失败', [
                'local_path' => $localPath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 更新文件同步状态
     */
    public function updateSyncStatus($fileId, $status, $qiniuUrl = null, $errorMessage = null)
    {
        try {
            $sql = "
                UPDATE qiniu_sync_files 
                SET sync_status = ?, 
                    sync_attempts = sync_attempts + 1,
                    last_sync_at = CURRENT_TIMESTAMP,
                    updated_at = CURRENT_TIMESTAMP
            ";
            $params = [$status];
            
            if ($qiniuUrl !== null) {
                $sql .= ", qiniu_url = ?";
                $params[] = $qiniuUrl;
            }
            
            if ($errorMessage !== null) {
                $sql .= ", error_message = ?";
                $params[] = $errorMessage;
            }
            
            $sql .= " WHERE id = ?";
            $params[] = $fileId;
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($params);
            
            if ($result) {
                $this->logger->info('文件同步状态更新', [
                    'file_id' => $fileId,
                    'status' => $status,
                    'qiniu_url' => $qiniuUrl
                ]);
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            $this->logger->error('更新文件同步状态失败', [
                'file_id' => $fileId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 获取待同步文件
     */
    public function getPendingFiles($limit = 10)
    {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM qiniu_sync_files 
                WHERE sync_status IN ('pending', 'failed') 
                    AND sync_attempts < 3
                ORDER BY created_at ASC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->error('获取待同步文件失败', ['error' => $e->getMessage()]);
            return [];
        }
    }
    
    /**
     * 获取文件ID
     */
    public function getFileId($localPath)
    {
        try {
            $stmt = $this->db->prepare("SELECT id FROM qiniu_sync_files WHERE local_path = ?");
            $stmt->execute([$localPath]);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $row ? $row['id'] : null;
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * 获取文件信息
     */
    public function getFileInfo($fileId)
    {
        try {
            $stmt = $this->db->prepare("SELECT * FROM qiniu_sync_files WHERE id = ?");
            $stmt->execute([$fileId]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->error('获取文件信息失败', [
                'file_id' => $fileId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * 删除文件记录
     */
    public function deleteFileRecord($localPath)
    {
        try {
            $stmt = $this->db->prepare("DELETE FROM qiniu_sync_files WHERE local_path = ?");
            $result = $stmt->execute([$localPath]);
            
            if ($result) {
                $this->logger->info('文件记录删除成功', ['local_path' => $localPath]);
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            $this->logger->error('删除文件记录失败', [
                'local_path' => $localPath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 获取文件统计
     */
    public function getFileStats()
    {
        try {
            $stmt = $this->db->query("
                SELECT 
                    COUNT(*) as total_files,
                    SUM(CASE WHEN sync_status = 'success' THEN 1 ELSE 0 END) as synced_files,
                    SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed_files,
                    SUM(CASE WHEN sync_status = 'pending' THEN 1 ELSE 0 END) as pending_files,
                    SUM(file_size) as total_size,
                    SUM(CASE WHEN sync_status = 'success' THEN file_size ELSE 0 END) as synced_size
                FROM qiniu_sync_files
            ");
            
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $this->logger->error('获取文件统计失败', ['error' => $e->getMessage()]);
            return [
                'total_files' => 0,
                'synced_files' => 0,
                'failed_files' => 0,
                'pending_files' => 0,
                'total_size' => 0,
                'synced_size' => 0
            ];
        }
    }
    
    /**
     * 获取总文件数
     */
    public function getTotalFiles($status = 'all', $directory = '')
    {
        try {
            $conditions = [];
            $params = [];

            // 状态筛选
            if ($status !== 'all') {
                $conditions[] = 'sync_status = ?';
                $params[] = $status;
            }

            // 目录筛选
            if (!empty($directory)) {
                $conditions[] = 'local_path LIKE ?';
                $params[] = $directory . '%';
            }

            // 构建WHERE子句
            $whereClause = '';
            if (!empty($conditions)) {
                $whereClause = 'WHERE ' . implode(' AND ', $conditions);
            }

            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM qiniu_sync_files $whereClause");
            $stmt->execute($params);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            return $row['count'];
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * 获取文件列表
     */
    public function getFiles($page = 1, $limit = 20, $status = 'all', $directory = '')
    {
        try {
            $offset = ($page - 1) * $limit;
            $conditions = [];
            $params = [];

            // 状态筛选
            if ($status !== 'all') {
                $conditions[] = 'sync_status = ?';
                $params[] = $status;
            }

            // 目录筛选
            if (!empty($directory)) {
                $conditions[] = 'local_path LIKE ?';
                $params[] = $directory . '%';
            }

            // 构建WHERE子句
            $whereClause = '';
            if (!empty($conditions)) {
                $whereClause = 'WHERE ' . implode(' AND ', $conditions);
            }

            // 添加分页参数
            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->db->prepare("
                SELECT * FROM qiniu_sync_files
                $whereClause
                ORDER BY updated_at DESC
                LIMIT ? OFFSET ?
            ");
            $stmt->execute($params);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 获取文件统计信息
     */
    public function getStats()
    {
        try {
            $stmt = $this->db->query("
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN sync_status = 'synced' THEN 1 ELSE 0 END) as synced,
                    SUM(CASE WHEN sync_status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed
                FROM qiniu_sync_files
            ");
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            return $row ?: [
                'total' => 0,
                'synced' => 0,
                'pending' => 0,
                'failed' => 0
            ];
        } catch (Exception $e) {
            return [
                'total' => 0,
                'synced' => 0,
                'pending' => 0,
                'failed' => 0
            ];
        }
    }
    
    /**
     * 清理失效文件记录
     */
    public function cleanupInvalidFiles()
    {
        try {
            $stmt = $this->db->query("SELECT id, local_path FROM qiniu_sync_files");
            $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $deletedCount = 0;
            foreach ($files as $file) {
                $fullPath = QINIU_SYNC_ROOT . '/' . ltrim($file['local_path'], '/');
                
                if (!file_exists($fullPath)) {
                    $deleteStmt = $this->db->prepare("DELETE FROM qiniu_sync_files WHERE id = ?");
                    if ($deleteStmt->execute([$file['id']])) {
                        $deletedCount++;
                    }
                }
            }
            
            $this->logger->info('清理失效文件记录完成', ['deleted_count' => $deletedCount]);
            return $deletedCount;
        } catch (Exception $e) {
            $this->logger->error('清理失效文件记录失败', ['error' => $e->getMessage()]);
            return 0;
        }
    }
    
    /**
     * 生成七牛云文件键
     */
    public function generateQiniuKey($localPath, $prefix = '')
    {
        // 移除路径前缀
        $key = ltrim($localPath, '/');
        
        // 添加前缀
        if (!empty($prefix)) {
            $key = rtrim($prefix, '/') . '/' . $key;
        }
        
        return $key;
    }
    
    /**
     * 获取文件MIME类型
     */
    public function getMimeType($filePath)
    {
        if (!file_exists($filePath)) {
            return null;
        }
        
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $filePath);
        finfo_close($finfo);
        
        return $mimeType;
    }
    
    /**
     * 监控文件变化
     */
    public function watchFiles($callback = null)
    {
        $this->logger->info('开始监控文件变化');
        
        $lastScan = [];
        
        while (true) {
            try {
                $currentFiles = $this->scanWatchPaths();
                
                foreach ($currentFiles as $file) {
                    $key = $file['local_path'];
                    
                    // 检查是否是新文件或已修改文件
                    if (!isset($lastScan[$key]) || $lastScan[$key]['hash'] !== $file['hash']) {
                        $this->logger->info('检测到文件变化', [
                            'file' => $file['local_path'],
                            'action' => isset($lastScan[$key]) ? 'modified' : 'created'
                        ]);
                        
                        if ($callback && is_callable($callback)) {
                            $callback($file, isset($lastScan[$key]) ? 'modified' : 'created');
                        }
                    }
                    
                    $lastScan[$key] = $file;
                }
                
                // 检查删除的文件
                foreach ($lastScan as $key => $file) {
                    $found = false;
                    foreach ($currentFiles as $currentFile) {
                        if ($currentFile['local_path'] === $key) {
                            $found = true;
                            break;
                        }
                    }
                    
                    if (!$found) {
                        $this->logger->info('检测到文件删除', ['file' => $key]);
                        
                        if ($callback && is_callable($callback)) {
                            $callback($file, 'deleted');
                        }
                        
                        unset($lastScan[$key]);
                    }
                }
                
                // 休眠一段时间再继续监控
                $interval = $this->config['sync']['sync_interval'] ?? 5;
                sleep($interval);
                
            } catch (Exception $e) {
                $this->logger->error('文件监控出错', ['error' => $e->getMessage()]);
                sleep(10); // 出错时等待更长时间
            }
        }
    }

    /**
     * 添加文件到同步队列
     */
    public function addFile($localPath, $qiniuKey = null)
    {
        try {
            // 检查文件是否存在
            if (!file_exists($localPath)) {
                $this->logger->error('文件不存在', ['path' => $localPath]);
                return false;
            }

            // 生成七牛云文件键名
            if ($qiniuKey === null) {
                // 获取配置前缀
                $prefix = '';
                if ($this->configManager) {
                    $prefix = $this->configManager->getConfig('qiniu.prefix', '');
                }

                $relativePath = str_replace(QINIU_SYNC_ROOT, '', $localPath);
                $relativePath = ltrim($relativePath, '/\\');
                $qiniuKey = $prefix . str_replace('\\', '/', $relativePath);
            }

            // 获取文件信息
            $fileSize = filesize($localPath);
            $fileHash = md5_file($localPath);
            $mimeType = $this->getMimeType($localPath);

            // 检查文件是否已存在
            $stmt = $this->db->prepare("SELECT id FROM qiniu_sync_files WHERE local_path = ?");
            $stmt->execute([$localPath]);

            if ($stmt->rowCount() > 0) {
                // 文件已存在，更新信息
                $stmt = $this->db->prepare("
                    UPDATE qiniu_sync_files
                    SET qiniu_key = ?, file_size = ?, file_hash = ?, mime_type = ?,
                        sync_status = 'pending', updated_at = NOW()
                    WHERE local_path = ?
                ");
                $result = $stmt->execute([$qiniuKey, $fileSize, $fileHash, $mimeType, $localPath]);

                if ($result) {
                    $this->logger->info('文件信息已更新', ['path' => $localPath]);
                    return true;
                } else {
                    $this->logger->error('更新文件信息失败', ['path' => $localPath]);
                    return false;
                }
            } else {
                // 添加新文件
                $stmt = $this->db->prepare("
                    INSERT INTO qiniu_sync_files
                    (local_path, qiniu_key, file_size, file_hash, mime_type, sync_status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, 'pending', NOW(), NOW())
                ");
                $result = $stmt->execute([$localPath, $qiniuKey, $fileSize, $fileHash, $mimeType]);

                if ($result) {
                    $this->logger->info('文件已添加到同步队列', [
                        'path' => $localPath,
                        'qiniu_key' => $qiniuKey,
                        'size' => $fileSize
                    ]);
                    return true;
                } else {
                    $this->logger->error('添加文件到同步队列失败', ['path' => $localPath]);
                    return false;
                }
            }

        } catch (Exception $e) {
            $this->logger->error('添加文件失败', [
                'path' => $localPath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 批量添加文件
     */
    public function addFiles($filePaths)
    {
        $successCount = 0;
        $failCount = 0;

        foreach ($filePaths as $filePath) {
            if ($this->addFile($filePath)) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        $this->logger->info('批量添加文件完成', [
            'success' => $successCount,
            'failed' => $failCount,
            'total' => count($filePaths)
        ]);

        return [
            'success' => $successCount,
            'failed' => $failCount,
            'total' => count($filePaths)
        ];
    }

    /**
     * 扫描目录并添加文件
     */
    public function scanAndAddDirectory($directory, $recursive = true)
    {
        try {
            if (!is_dir($directory)) {
                $this->logger->error('目录不存在', ['directory' => $directory]);
                return false;
            }

            $files = [];
            // 获取允许的文件扩展名
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'txt', 'doc', 'docx'];
            if ($this->configManager) {
                $configExtensions = $this->configManager->getConfig('sync.allowed_extensions');
                if ($configExtensions && is_array($configExtensions)) {
                    $allowedExtensions = $configExtensions;
                }
            }

            if ($recursive) {
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
                );
            } else {
                $iterator = new DirectoryIterator($directory);
            }

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $extension = strtolower($file->getExtension());
                    if (in_array($extension, $allowedExtensions)) {
                        $files[] = $file->getPathname();
                    }
                }
            }

            if (empty($files)) {
                $this->logger->info('目录中没有找到可同步的文件', ['directory' => $directory]);
                return ['success' => 0, 'failed' => 0, 'total' => 0];
            }

            return $this->addFiles($files);

        } catch (Exception $e) {
            $this->logger->error('扫描目录失败', [
                'directory' => $directory,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取数据库连接
     */
    public function getDatabase()
    {
        return $this->db;
    }
}
