<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置验证测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i> 配置验证测试
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="accessKey" class="form-label">AccessKey</label>
                            <input type="text" class="form-control" id="accessKey" value="28l4nKQqPVyeow01DhPB9JYKPUKvQWYW-FlbxO">
                        </div>
                        
                        <div class="mb-3">
                            <label for="secretKey" class="form-label">SecretKey</label>
                            <input type="password" class="form-control" id="secretKey" value="••••••••••••••••••••••••••••••••••••••••••••">
                        </div>
                        
                        <div class="mb-3">
                            <label for="bucket" class="form-label">Bucket</label>
                            <input type="text" class="form-control" id="bucket" value="yubanshanqin">
                        </div>
                        
                        <div class="mb-3">
                            <label for="domain" class="form-label">Domain</label>
                            <input type="text" class="form-control" id="domain" value="img.seeweb3.cn">
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button id="validateBtn" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 验证配置
                            </button>
                            <button id="saveBtn" class="btn btn-success">
                                <i class="bi bi-save"></i> 保存配置
                            </button>
                            <button id="testDirectBtn" class="btn btn-warning">
                                <i class="bi bi-lightning"></i> 直接测试
                            </button>
                        </div>
                        
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="alert alert-${isSuccess ? 'success' : 'danger'}">
                    ${message}
                </div>
            `;
        }
        
        function getConfig() {
            return {
                access_key: document.getElementById('accessKey').value,
                secret_key: document.getElementById('secretKey').value,
                bucket: document.getElementById('bucket').value,
                domain: document.getElementById('domain').value
            };
        }
        
        // 验证配置
        document.getElementById('validateBtn').addEventListener('click', function() {
            fetch('ajax_test.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'validate_config'})
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed data:', data);
                    
                    if (data.valid) {
                        showResult('配置验证成功！', true);
                    } else {
                        let errorMsg = '配置验证失败：<br>' + data.errors.join('<br>');
                        if (data.need_install_sdk) {
                            errorMsg += '<br><br><a href="../install_sdk.php" class="btn btn-primary btn-sm" target="_blank">安装七牛云SDK</a>';
                        }
                        showResult(errorMsg, false);
                    }
                } catch (e) {
                    showResult('响应解析失败：' + e.message + '<br>原始响应：<pre>' + text + '</pre>', false);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                showResult('请求失败：' + error.message, false);
            });
        });
        
        // 保存配置
        document.getElementById('saveBtn').addEventListener('click', function() {
            const config = getConfig();
            
            fetch('ajax_test.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({action: 'save_config', config: config})
            })
            .then(response => response.json())
            .then(data => {
                showResult(data.success ? '配置保存成功！' : '保存失败：' + data.message, data.success);
            })
            .catch(error => {
                showResult('请求失败：' + error.message, false);
            });
        });
        
        // 直接测试（PHP方式）
        document.getElementById('testDirectBtn').addEventListener('click', function() {
            window.open('test_direct.php', '_blank');
        });
    </script>
</body>
</html>
