<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复JSON错误</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <h2>修复JSON错误</h2>
        
        <?php
        session_start();
        
        // 自动设置登录状态
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = 'admin';
        
        echo "<div class='alert alert-success'>✓ 会话已设置</div>";
        ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>问题诊断</h5>
            </div>
            <div class="card-body">
                <h6>1. 会话状态检查</h6>
                <p>登录状态: <?php echo isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] ? '✓ 已登录' : '✗ 未登录'; ?></p>
                <p>用户名: <?php echo $_SESSION['admin_username'] ?? '未设置'; ?></p>
                
                <h6>2. 文件检查</h6>
                <p>ajax.php: <?php echo file_exists('ajax.php') ? '✓ 存在' : '✗ 不存在'; ?></p>
                <p>bootstrap.php: <?php echo file_exists('../bootstrap.php') ? '✓ 存在' : '✗ 不存在'; ?></p>
                
                <h6>3. PHP错误检查</h6>
                <?php
                ob_start();
                try {
                    require_once dirname(__DIR__) . '/bootstrap.php';
                    $qiniuSync = new QiniuSync();
                    echo "<p>✓ 插件加载成功</p>";
                } catch (Exception $e) {
                    echo "<p>✗ 插件加载失败: " . htmlspecialchars($e->getMessage()) . "</p>";
                } catch (Error $e) {
                    echo "<p>✗ PHP错误: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
                $output = ob_get_clean();
                echo $output;
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>AJAX测试</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-primary" onclick="testAjax()">测试AJAX请求</button>
                <button class="btn btn-secondary" onclick="testDirectAccess()">测试直接访问</button>
                <div class="mt-3">
                    <h6>响应结果:</h6>
                    <pre id="result" style="background: #f8f9fa; padding: 10px; border-radius: 5px; min-height: 100px;"></pre>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>解决方案</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>确保会话正确设置</strong> - 已自动完成</li>
                    <li><strong>检查AJAX请求</strong> - 点击上面的测试按钮</li>
                    <li><strong>查看错误详情</strong> - 在浏览器开发者工具的Network标签页中查看请求详情</li>
                    <li><strong>访问管理面板</strong> - <a href="index.php" class="btn btn-success btn-sm">进入管理面板</a></li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function showResult(data) {
            document.getElementById('result').textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }

        function testAjax() {
            const testData = {
                action: 'validate_config'
            };

            fetch('ajax.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response type:', response.headers.get('content-type'));
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    showResult({
                        success: true,
                        message: 'JSON解析成功',
                        data: data
                    });
                } catch (e) {
                    showResult({
                        success: false,
                        error: 'JSON解析失败',
                        raw_response: text.substring(0, 500) + (text.length > 500 ? '...' : ''),
                        parse_error: e.message
                    });
                }
            })
            .catch(error => {
                showResult({
                    success: false,
                    error: 'Fetch请求失败',
                    message: error.message
                });
            });
        }

        function testDirectAccess() {
            window.open('ajax.php', '_blank');
        }
    </script>
</body>
</html>
