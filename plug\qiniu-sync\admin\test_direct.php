<?php
/**
 * 直接配置验证测试
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>七牛云配置验证测试</h2>";

try {
    // 引入插件核心文件
    require_once dirname(__DIR__) . '/bootstrap.php';
    echo "<p>✓ 插件核心文件加载成功</p>";
    
    // 创建插件实例
    $qiniuSync = new QiniuSync();
    echo "<p>✓ 插件实例创建成功</p>";
    
    // 获取配置管理器
    $configManager = $qiniuSync->getConfigManager();
    echo "<p>✓ 配置管理器获取成功</p>";
    
    // 检查当前配置
    echo "<h3>当前配置：</h3>";
    $currentConfig = [
        'access_key' => $configManager->getConfig('qiniu.access_key'),
        'secret_key' => $configManager->getConfig('qiniu.secret_key'),
        'bucket' => $configManager->getConfig('qiniu.bucket'),
        'domain' => $configManager->getConfig('qiniu.domain')
    ];
    
    echo "<pre>";
    foreach ($currentConfig as $key => $value) {
        if ($key === 'secret_key' && $value) {
            echo "$key: " . str_repeat('*', strlen($value)) . "\n";
        } else {
            echo "$key: " . ($value ?: '(未设置)') . "\n";
        }
    }
    echo "</pre>";
    
    // 验证配置
    echo "<h3>配置验证结果：</h3>";
    $result = $configManager->validateQiniuConfig();
    
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
    // 检查vendor目录
    echo "<h3>依赖检查：</h3>";
    $vendorPath = dirname(__DIR__) . '/vendor/autoload.php';
    if (file_exists($vendorPath)) {
        echo "<p>✓ 七牛云SDK已安装</p>";
        
        // 尝试加载SDK
        try {
            require_once $vendorPath;
            echo "<p>✓ SDK加载成功</p>";
            
            // 检查类是否存在
            if (class_exists('\\Qiniu\\Auth')) {
                echo "<p>✓ Qiniu\\Auth类可用</p>";
            } else {
                echo "<p>✗ Qiniu\\Auth类不可用</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>✗ SDK加载失败: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>✗ 七牛云SDK未安装</p>";
        echo "<p>SDK路径: $vendorPath</p>";
        echo "<p><a href='../install_sdk.php' target='_blank'>点击安装SDK</a></p>";
    }
    
    // 测试数据库连接
    echo "<h3>数据库连接测试：</h3>";
    try {
        $db = $configManager->getDatabase();
        echo "<p>✓ 数据库连接成功</p>";
        
        // 检查配置表
        $stmt = $db->query("SHOW TABLES LIKE 'qiniu_sync_config'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✓ 配置表存在</p>";
            
            // 查看配置数据
            $stmt = $db->query("SELECT config_key, config_type, is_encrypted FROM qiniu_sync_config");
            $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p>配置项数量: " . count($configs) . "</p>";
            if (count($configs) > 0) {
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>配置键</th><th>类型</th><th>加密</th></tr>";
                foreach ($configs as $config) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($config['config_key']) . "</td>";
                    echo "<td>" . htmlspecialchars($config['config_type']) . "</td>";
                    echo "<td>" . ($config['is_encrypted'] ? '是' : '否') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p>✗ 配置表不存在</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p>✗ 错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<p>✗ PHP错误: " . $e->getMessage() . "</p>";
    echo "<p>文件: " . $e->getFile() . " 行号: " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<p><a href='test_config.php'>返回测试页面</a></p>";
echo "<p><a href='index.php'>返回管理面板</a></p>";
?>
