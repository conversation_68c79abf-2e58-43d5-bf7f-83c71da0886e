<?php
/**
 * 日志查看页面
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 检查权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

try {
    $qiniuSync = new QiniuSync();
    $logger = $qiniuSync->getLogger();
    
    // 获取日志参数
    $page = $_GET['page'] ?? 1;
    $limit = $_GET['limit'] ?? 50;
    $level = $_GET['level'] ?? 'all';
    $date = $_GET['date'] ?? date('Y-m-d');
    
    // 获取日志列表
    $logs = $logger->getLogs($page, $limit, $level, $date);
    $totalLogs = $logger->getTotalLogs($level, $date);
    $totalPages = ceil($totalLogs / $limit);
    
    // 获取日志统计
    $logStats = $logger->getLogStatsByDate($date);
    
    // 获取可用的日志日期
    $availableDates = $logger->getAvailableDates();
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $logs = [];
    $totalLogs = 0;
    $totalPages = 0;
    $logStats = [
        'total' => 0,
        'error' => 0,
        'warning' => 0,
        'info' => 0,
        'debug' => 0
    ];
    $availableDates = [];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看 - 七牛云同步插件</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .log-error { border-left: 4px solid #dc3545; }
        .log-warning { border-left: 4px solid #ffc107; }
        .log-info { border-left: 4px solid #0dcaf0; }
        .log-debug { border-left: 4px solid #6c757d; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-cloud-upload"></i> 七牛云同步插件
            </a>
            <span class="navbar-text">日志查看</span>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> 错误: <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- 日志统计 -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($logStats['total']); ?></h5>
                        <p class="card-text">总日志数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center text-danger">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($logStats['error']); ?></h5>
                        <p class="card-text">错误</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center text-warning">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($logStats['warning']); ?></h5>
                        <p class="card-text">警告</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center text-info">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($logStats['info']); ?></h5>
                        <p class="card-text">信息</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center text-secondary">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo number_format($logStats['debug']); ?></h5>
                        <p class="card-text">调试</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title"><?php echo count($availableDates); ?></h5>
                        <p class="card-text">日志天数</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和操作 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="d-flex gap-2">
                            <select class="form-select" id="dateFilter" style="width: auto;">
                                <?php foreach ($availableDates as $availableDate): ?>
                                    <option value="<?php echo $availableDate; ?>" <?php echo $availableDate === $date ? 'selected' : ''; ?>>
                                        <?php echo $availableDate; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <select class="form-select" id="levelFilter" style="width: auto;">
                                <option value="all" <?php echo $level === 'all' ? 'selected' : ''; ?>>所有级别</option>
                                <option value="error" <?php echo $level === 'error' ? 'selected' : ''; ?>>错误</option>
                                <option value="warning" <?php echo $level === 'warning' ? 'selected' : ''; ?>>警告</option>
                                <option value="info" <?php echo $level === 'info' ? 'selected' : ''; ?>>信息</option>
                                <option value="debug" <?php echo $level === 'debug' ? 'selected' : ''; ?>>调试</option>
                            </select>
                            <button class="btn btn-outline-primary" onclick="refreshLogs()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex gap-2 justify-content-end">
                            <button class="btn btn-warning" onclick="clearLogs()">
                                <i class="bi bi-trash"></i> 清空日志
                            </button>
                            <button class="btn btn-info" onclick="downloadLogs()">
                                <i class="bi bi-download"></i> 下载日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-journal-text"></i> 日志记录
                    <small class="text-muted">(<?php echo $date; ?> - 共 <?php echo number_format($totalLogs); ?> 条)</small>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($logs)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <p class="text-muted mt-2">暂无日志记录</p>
                    </div>
                <?php else: ?>
                    <div class="log-container">
                        <?php foreach ($logs as $log): ?>
                            <div class="card mb-2 log-entry log-<?php echo $log['level']; ?>">
                                <div class="card-body py-2">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <small class="text-muted"><?php echo date('H:i:s', strtotime($log['created_at'])); ?></small>
                                        </div>
                                        <div class="col-md-1">
                                            <?php
                                            $levelClass = '';
                                            $levelIcon = '';
                                            switch ($log['level']) {
                                                case 'error':
                                                    $levelClass = 'danger';
                                                    $levelIcon = 'exclamation-triangle';
                                                    break;
                                                case 'warning':
                                                    $levelClass = 'warning';
                                                    $levelIcon = 'exclamation-circle';
                                                    break;
                                                case 'info':
                                                    $levelClass = 'info';
                                                    $levelIcon = 'info-circle';
                                                    break;
                                                case 'debug':
                                                    $levelClass = 'secondary';
                                                    $levelIcon = 'bug';
                                                    break;
                                                default:
                                                    $levelClass = 'secondary';
                                                    $levelIcon = 'circle';
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $levelClass; ?>">
                                                <i class="bi bi-<?php echo $levelIcon; ?>"></i>
                                                <?php echo strtoupper($log['level']); ?>
                                            </span>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="log-message">
                                                <?php echo htmlspecialchars($log['message']); ?>
                                            </div>
                                            <?php if (!empty($log['context'])): ?>
                                                <div class="mt-1">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" 
                                                            data-bs-toggle="collapse" data-bs-target="#context-<?php echo $log['id']; ?>">
                                                        <i class="bi bi-chevron-down"></i> 详情
                                                    </button>
                                                </div>
                                                <div class="collapse mt-2" id="context-<?php echo $log['id']; ?>">
                                                    <div class="card card-body bg-light">
                                                        <pre class="mb-0"><?php echo htmlspecialchars(json_encode(json_decode($log['context']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&level=<?php echo $level; ?>&date=<?php echo $date; ?>&limit=<?php echo $limit; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 刷新日志
        function refreshLogs() {
            location.reload();
        }

        // 日期筛选
        document.getElementById('dateFilter').addEventListener('change', function() {
            updateFilters();
        });

        // 级别筛选
        document.getElementById('levelFilter').addEventListener('change', function() {
            updateFilters();
        });

        function updateFilters() {
            const date = document.getElementById('dateFilter').value;
            const level = document.getElementById('levelFilter').value;
            const url = new URL(window.location);
            url.searchParams.set('date', date);
            url.searchParams.set('level', level);
            url.searchParams.set('page', '1');
            window.location = url;
        }

        // 清空日志
        function clearLogs() {
            if (confirm('确定要清空所有日志吗？此操作不可恢复。')) {
                alert('功能开发中...');
            }
        }

        // 下载日志
        function downloadLogs() {
            const date = document.getElementById('dateFilter').value;
            const level = document.getElementById('levelFilter').value;
            alert('功能开发中...');
        }

        // 自动刷新（可选）
        // setInterval(refreshLogs, 30000); // 每30秒刷新一次
    </script>
</body>
</html>
