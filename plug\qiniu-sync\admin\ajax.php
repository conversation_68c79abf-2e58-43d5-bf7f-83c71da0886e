<?php
/**
 * 七牛云同步插件AJAX处理器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 检查权限
session_start();

// 如果没有登录，自动设置登录状态（开发环境）
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    // 在生产环境中应该返回错误，这里为了调试自动登录
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_username'] = 'admin';
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 开启错误缓冲，防止意外输出
ob_start();

try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }

    // 获取请求数据
    $input = file_get_contents('php://input');

    if (empty($input)) {
        throw new Exception('请求数据为空');
    }

    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('JSON解析失败: ' . json_last_error_msg());
    }

    if (!$data || !isset($data['action'])) {
        throw new Exception('缺少action参数');
    }
    
    $action = $data['action'];
    $qiniuSync = new QiniuSync();
    
    switch ($action) {
        case 'test_connection':
            $result = $qiniuSync->getSyncManager()->testConnection();
            break;
            
        case 'save_config':
            if (!isset($data['config'])) {
                throw new Exception('缺少配置数据');
            }
            $result = $qiniuSync->getConfigManager()->updateQiniuConfig($data['config']);
            $result = ['success' => $result, 'message' => $result ? '配置保存成功' : '配置保存失败'];
            break;
            
        case 'validate_config':
            $result = $qiniuSync->getConfigManager()->validateQiniuConfig();
            // 确保返回格式正确
            if (!isset($result['valid'])) {
                $result = ['valid' => false, 'errors' => ['配置验证返回格式错误']];
            }
            break;
            
        case 'start_sync':
            $result = $qiniuSync->getSyncManager()->start();
            break;
            
        case 'stop_sync':
            $result = $qiniuSync->getSyncManager()->stop();
            break;

        case 'manual_sync':
            $limit = $data['limit'] ?? 10;
            $result = $qiniuSync->getSyncManager()->manualSync($limit);
            break;
            
        case 'sync_all_files':
            $result = $qiniuSync->getSyncManager()->syncAllFiles();
            break;
            
        case 'cleanup_files':
            $deletedCount = $qiniuSync->getFileManager()->cleanupInvalidFiles();
            $result = ['success' => true, 'deleted_count' => $deletedCount];
            break;
            
        case 'get_files':
            $result = getFilesHtml($qiniuSync);
            break;
            
        case 'get_queue':
            $result = getQueueHtml($qiniuSync);
            break;
            
        case 'get_logs':
            $result = getLogsHtml($qiniuSync);
            break;
            
        case 'retry_failed_jobs':
            $jobIds = $data['job_ids'] ?? [];
            $retryCount = $qiniuSync->getQueueManager()->retryFailedJobs($jobIds);
            $result = ['success' => true, 'retry_count' => $retryCount];
            break;
            
        case 'clear_queue':
            $status = $data['status'] ?? null;
            $deletedCount = $qiniuSync->getQueueManager()->clearQueue($status);
            $result = ['success' => true, 'deleted_count' => $deletedCount];
            break;
            
        case 'get_file_details':
            if (!isset($data['file_id'])) {
                throw new Exception('缺少文件ID');
            }
            $fileInfo = $qiniuSync->getFileManager()->getFileInfo($data['file_id']);
            $result = ['success' => true, 'data' => $fileInfo];
            break;

        case 'save_sync_dirs':
            $result = saveSyncDirsConfig($qiniuSync, $data);
            break;

        case 'scan_all_dirs':
            $result = scanAllDirectories($qiniuSync);
            break;

        default:
            throw new Exception('未知的操作: ' . $action);
    }
    
    // 清理输出缓冲区
    ob_clean();
    echo json_encode($result, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 清理输出缓冲区
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    // 清理输出缓冲区
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => 'PHP Error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 生成文件列表HTML
 */
function getFilesHtml($qiniuSync)
{
    try {
        $fileManager = $qiniuSync->getFileManager();
        $files = $fileManager->getPendingFiles(50); // 获取前50个待处理文件
        $stats = $fileManager->getFileStats();
        
        $html = '<div class="row mb-3">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h6>文件统计</h6>
                    <button class="btn btn-sm btn-primary" onclick="loadFilesContent()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($stats['total_files'] ?? 0) . '</h5>
                                <small>总文件数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($stats['synced_files'] ?? 0) . '</h5>
                                <small>已同步</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($stats['failed_files'] ?? 0) . '</h5>
                                <small>失败</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($stats['pending_files'] ?? 0) . '</h5>
                                <small>待处理</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
        
        if (empty($files)) {
            $html .= '<div class="alert alert-info">暂无待处理文件</div>';
        } else {
            $html .= '<div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>文件路径</th>
                            <th>七牛云键</th>
                            <th>文件大小</th>
                            <th>同步状态</th>
                            <th>尝试次数</th>
                            <th>最后同步时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>';
            
            foreach ($files as $file) {
                $statusClass = '';
                $statusText = '';
                switch ($file['sync_status']) {
                    case 'success':
                        $statusClass = 'text-success';
                        $statusText = '成功';
                        break;
                    case 'failed':
                        $statusClass = 'text-danger';
                        $statusText = '失败';
                        break;
                    case 'pending':
                        $statusClass = 'text-warning';
                        $statusText = '待处理';
                        break;
                    case 'syncing':
                        $statusClass = 'text-info';
                        $statusText = '同步中';
                        break;
                }
                
                $fileSize = $file['file_size'] ? number_format($file['file_size'] / 1024, 2) . ' KB' : '-';
                $lastSync = $file['last_sync_at'] ? date('Y-m-d H:i:s', strtotime($file['last_sync_at'])) : '-';
                
                $html .= '<tr>
                    <td>' . htmlspecialchars($file['local_path']) . '</td>
                    <td>' . htmlspecialchars($file['qiniu_key']) . '</td>
                    <td>' . $fileSize . '</td>
                    <td><span class="' . $statusClass . '">' . $statusText . '</span></td>
                    <td>' . $file['sync_attempts'] . '</td>
                    <td>' . $lastSync . '</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="viewFileDetails(' . $file['id'] . ')">
                            <i class="bi bi-eye"></i>
                        </button>';
                
                if ($file['qiniu_url']) {
                    $html .= ' <a href="' . htmlspecialchars($file['qiniu_url']) . '" target="_blank" class="btn btn-sm btn-success">
                        <i class="bi bi-link"></i>
                    </a>';
                }
                
                $html .= '</td>
                </tr>';
            }
            
            $html .= '</tbody>
                </table>
            </div>';
        }
        
        return ['success' => true, 'html' => $html];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 生成队列列表HTML
 */
function getQueueHtml($qiniuSync)
{
    try {
        $queueManager = $qiniuSync->getQueueManager();
        $queueStats = $queueManager->getQueueSize();
        $jobs = $queueManager->getQueueJobs([], 50);
        
        $html = '<div class="row mb-3">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h6>队列统计</h6>
                    <div>
                        <button class="btn btn-sm btn-warning" onclick="retryFailedJobs()">
                            <i class="bi bi-arrow-repeat"></i> 重试失败任务
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="clearQueue()">
                            <i class="bi bi-trash"></i> 清空队列
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="loadQueueContent()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($queueStats['total'] ?? 0) . '</h5>
                                <small>总任务数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($queueStats['pending'] ?? 0) . '</h5>
                                <small>待处理</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($queueStats['processing'] ?? 0) . '</h5>
                                <small>处理中</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($queueStats['completed'] ?? 0) . '</h5>
                                <small>已完成</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5>' . ($queueStats['failed'] ?? 0) . '</h5>
                                <small>失败</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
        
        if (empty($jobs)) {
            $html .= '<div class="alert alert-info">队列为空</div>';
        } else {
            $html .= '<div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>文件路径</th>
                            <th>操作</th>
                            <th>状态</th>
                            <th>优先级</th>
                            <th>尝试次数</th>
                            <th>创建时间</th>
                            <th>错误信息</th>
                        </tr>
                    </thead>
                    <tbody>';
            
            foreach ($jobs as $job) {
                $statusClass = '';
                $statusText = '';
                switch ($job['status']) {
                    case 'completed':
                        $statusClass = 'text-success';
                        $statusText = '已完成';
                        break;
                    case 'failed':
                        $statusClass = 'text-danger';
                        $statusText = '失败';
                        break;
                    case 'pending':
                        $statusClass = 'text-warning';
                        $statusText = '待处理';
                        break;
                    case 'processing':
                        $statusClass = 'text-info';
                        $statusText = '处理中';
                        break;
                }
                
                $actionText = $job['action'] === 'upload' ? '上传' : '删除';
                $createdAt = date('Y-m-d H:i:s', strtotime($job['created_at']));
                $errorMessage = $job['error_message'] ? htmlspecialchars(substr($job['error_message'], 0, 50)) . '...' : '-';
                
                $html .= '<tr>
                    <td>' . $job['id'] . '</td>
                    <td>' . htmlspecialchars($job['file_path']) . '</td>
                    <td>' . $actionText . '</td>
                    <td><span class="' . $statusClass . '">' . $statusText . '</span></td>
                    <td>' . $job['priority'] . '</td>
                    <td>' . $job['attempts'] . '/' . $job['max_attempts'] . '</td>
                    <td>' . $createdAt . '</td>
                    <td>' . $errorMessage . '</td>
                </tr>';
            }
            
            $html .= '</tbody>
                </table>
            </div>';
        }
        
        return ['success' => true, 'html' => $html];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 生成日志列表HTML
 */
function getLogsHtml($qiniuSync)
{
    try {
        $logger = $qiniuSync->getLogger();
        $logs = $logger->getLogsFromDatabase([], 50);
        
        $html = '<div class="row mb-3">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h6>最近日志</h6>
                    <button class="btn btn-sm btn-primary" onclick="loadLogsContent()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>
        </div>';
        
        if (empty($logs)) {
            $html .= '<div class="alert alert-info">暂无日志记录</div>';
        } else {
            $html .= '<div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>级别</th>
                            <th>类型</th>
                            <th>消息</th>
                            <th>IP地址</th>
                        </tr>
                    </thead>
                    <tbody>';
            
            foreach ($logs as $log) {
                $levelClass = '';
                switch ($log['log_level']) {
                    case 'error':
                        $levelClass = 'text-danger';
                        break;
                    case 'warning':
                        $levelClass = 'text-warning';
                        break;
                    case 'info':
                        $levelClass = 'text-info';
                        break;
                    case 'debug':
                        $levelClass = 'text-muted';
                        break;
                }
                
                $createdAt = date('Y-m-d H:i:s', strtotime($log['created_at']));
                $message = htmlspecialchars(substr($log['message'], 0, 100));
                if (strlen($log['message']) > 100) {
                    $message .= '...';
                }
                
                $html .= '<tr>
                    <td>' . $createdAt . '</td>
                    <td><span class="' . $levelClass . '">' . strtoupper($log['log_level']) . '</span></td>
                    <td>' . htmlspecialchars($log['log_type']) . '</td>
                    <td>' . $message . '</td>
                    <td>' . htmlspecialchars($log['ip_address'] ?: '-') . '</td>
                </tr>';
            }
            
            $html .= '</tbody>
                </table>
            </div>';
        }
        
        return ['success' => true, 'html' => $html];

    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * 保存同步目录配置
 */
function saveSyncDirsConfig($qiniuSync, $data)
{
    try {
        $configManager = $qiniuSync->getConfigManager();

        // 验证目录
        $syncDirs = $data['sync_dirs'] ?? [];
        $validDirs = [];

        foreach ($syncDirs as $dir) {
            $dir = trim($dir);
            if (empty($dir)) continue;

            // 如果目录不存在，尝试创建
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    throw new Exception("无法创建目录: $dir");
                }
            }

            if (!is_readable($dir)) {
                throw new Exception("目录不可读: $dir");
            }

            $validDirs[] = $dir;
        }

        if (empty($validDirs)) {
            throw new Exception('至少需要配置一个有效的同步目录');
        }

        // 保存配置
        $configManager->updateConfig('sync.directories', $validDirs);
        $configManager->updateConfig('sync.allowed_extensions', $data['allowed_extensions'] ?? ['jpg','jpeg','png','gif']);
        $configManager->updateConfig('sync.max_file_size', $data['max_file_size'] ?? 10);
        $configManager->updateConfig('sync.auto_scan', $data['auto_scan'] ?? false);
        $configManager->updateConfig('sync.delete_after_sync', $data['delete_after_sync'] ?? false);

        return [
            'success' => true,
            'message' => '同步目录配置保存成功',
            'valid_dirs' => $validDirs
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * 扫描所有配置的目录
 */
function scanAllDirectories($qiniuSync)
{
    try {
        $configManager = $qiniuSync->getConfigManager();
        $fileManager = $qiniuSync->getFileManager();

        $syncDirs = $configManager->getConfig('sync.directories', []);
        $allowedExtensions = $configManager->getConfig('sync.allowed_extensions', ['jpg','jpeg','png','gif']);

        if (empty($syncDirs)) {
            throw new Exception('未配置同步目录');
        }

        $totalFiles = 0;
        $addedFiles = 0;

        foreach ($syncDirs as $dir) {
            if (!is_dir($dir)) {
                continue;
            }

            $result = scanDirectoryForFiles($dir, $fileManager, $allowedExtensions);
            $totalFiles += $result['total'];
            $addedFiles += $result['added'];
        }

        return [
            'success' => true,
            'message' => '目录扫描完成',
            'total' => $totalFiles,
            'added' => $addedFiles,
            'scanned_dirs' => count($syncDirs)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}
?>
