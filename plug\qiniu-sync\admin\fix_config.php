<?php
/**
 * 配置修复工具
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入插件核心文件
require_once dirname(__DIR__) . '/bootstrap.php';

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=utf-8');
    
    try {
        $action = $_POST['action'] ?? '';
        $qiniuSync = new QiniuSync();
        $configManager = $qiniuSync->getConfigManager();
        
        switch ($action) {
            case 'update_config':
                $config = [
                    'access_key' => $_POST['access_key'] ?? '',
                    'secret_key' => $_POST['secret_key'] ?? '',
                    'bucket' => $_POST['bucket'] ?? '',
                    'domain' => $_POST['domain'] ?? '',
                    'prefix' => $_POST['prefix'] ?? '',
                    'region' => $_POST['region'] ?? 'z0',
                    'use_https' => isset($_POST['use_https']),
                    'use_cdn_domain' => isset($_POST['use_cdn_domain'])
                ];
                
                $result = $configManager->updateQiniuConfig($config);
                echo json_encode(['success' => $result, 'message' => $result ? '配置更新成功' : '配置更新失败']);
                break;
                
            case 'validate_config':
                $config = [
                    'access_key' => $_POST['access_key'] ?? '',
                    'secret_key' => $_POST['secret_key'] ?? '',
                    'bucket' => $_POST['bucket'] ?? '',
                    'domain' => $_POST['domain'] ?? ''
                ];
                
                $result = $configManager->validateQiniuConfig($config);
                echo json_encode($result);
                break;
                
            case 'clear_config':
                // 清空所有七牛云配置
                $clearConfigs = [
                    'qiniu.access_key' => '',
                    'qiniu.secret_key' => '',
                    'qiniu.bucket' => '',
                    'qiniu.domain' => '',
                    'qiniu.prefix' => '',
                    'qiniu.region' => 'z0',
                    'qiniu.use_https' => true,
                    'qiniu.use_cdn_domain' => true
                ];
                
                $result = $configManager->updateConfigs($clearConfigs);
                echo json_encode(['success' => $result, 'message' => $result ? '配置已清空' : '清空失败']);
                break;
                
            default:
                throw new Exception('未知操作');
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// 获取当前配置
try {
    $qiniuSync = new QiniuSync();
    $configManager = $qiniuSync->getConfigManager();
    
    $currentConfig = [
        'access_key' => $configManager->getConfig('qiniu.access_key', ''),
        'secret_key' => $configManager->getConfig('qiniu.secret_key', ''),
        'bucket' => $configManager->getConfig('qiniu.bucket', ''),
        'domain' => $configManager->getConfig('qiniu.domain', ''),
        'prefix' => $configManager->getConfig('qiniu.prefix', ''),
        'region' => $configManager->getConfig('qiniu.region', 'z0'),
        'use_https' => $configManager->getConfig('qiniu.use_https', true),
        'use_cdn_domain' => $configManager->getConfig('qiniu.use_cdn_domain', true)
    ];
} catch (Exception $e) {
    $currentConfig = [
        'access_key' => '',
        'secret_key' => '',
        'bucket' => '',
        'domain' => '',
        'prefix' => '',
        'region' => 'z0',
        'use_https' => true,
        'use_cdn_domain' => true
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>七牛云配置修复工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-tools"></i> 七牛云配置修复工具
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i> 七牛云配置
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="configForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="accessKey" class="form-label">AccessKey *</label>
                                        <input type="text" class="form-control" id="accessKey" name="access_key" 
                                               value="<?php echo htmlspecialchars($currentConfig['access_key']); ?>" required>
                                        <div class="form-text">七牛云访问密钥</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="secretKey" class="form-label">SecretKey *</label>
                                        <input type="password" class="form-control" id="secretKey" name="secret_key" 
                                               value="<?php echo htmlspecialchars($currentConfig['secret_key']); ?>" required>
                                        <div class="form-text">七牛云私钥</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bucket" class="form-label">Bucket *</label>
                                        <input type="text" class="form-control" id="bucket" name="bucket" 
                                               value="<?php echo htmlspecialchars($currentConfig['bucket']); ?>" required>
                                        <div class="form-text">存储桶名称</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="domain" class="form-label">Domain *</label>
                                        <input type="text" class="form-control" id="domain" name="domain" 
                                               value="<?php echo htmlspecialchars($currentConfig['domain']); ?>" required>
                                        <div class="form-text">绑定的访问域名</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="prefix" class="form-label">文件前缀</label>
                                        <input type="text" class="form-control" id="prefix" name="prefix" 
                                               value="<?php echo htmlspecialchars($currentConfig['prefix']); ?>">
                                        <div class="form-text">可选，如：images/</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="region" class="form-label">存储区域</label>
                                        <select class="form-select" id="region" name="region">
                                            <option value="z0" <?php echo $currentConfig['region'] === 'z0' ? 'selected' : ''; ?>>华东-浙江</option>
                                            <option value="z1" <?php echo $currentConfig['region'] === 'z1' ? 'selected' : ''; ?>>华北-河北</option>
                                            <option value="z2" <?php echo $currentConfig['region'] === 'z2' ? 'selected' : ''; ?>>华南-广东</option>
                                            <option value="na0" <?php echo $currentConfig['region'] === 'na0' ? 'selected' : ''; ?>>北美-洛杉矶</option>
                                            <option value="as0" <?php echo $currentConfig['region'] === 'as0' ? 'selected' : ''; ?>>亚太-新加坡</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="useHttps" name="use_https" 
                                               <?php echo $currentConfig['use_https'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="useHttps">使用HTTPS</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="useCdnDomain" name="use_cdn_domain" 
                                               <?php echo $currentConfig['use_cdn_domain'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="useCdnDomain">使用CDN域名</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="button" id="validateBtn" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> 验证配置
                                </button>
                                <button type="button" id="saveBtn" class="btn btn-success">
                                    <i class="bi bi-save"></i> 保存配置
                                </button>
                                <button type="button" id="clearBtn" class="btn btn-warning">
                                    <i class="bi bi-trash"></i> 清空配置
                                </button>
                                <a href="test_direct.php" class="btn btn-info" target="_blank">
                                    <i class="bi bi-bug"></i> 调试测试
                                </a>
                            </div>
                        </form>
                        
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle"></i> 配置说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6>获取七牛云配置信息：</h6>
                        <ol>
                            <li>登录 <a href="https://portal.qiniu.com/" target="_blank">七牛云控制台</a></li>
                            <li>进入"密钥管理"获取 AccessKey 和 SecretKey</li>
                            <li>进入"对象存储"创建或查看 Bucket 名称</li>
                            <li>在 Bucket 设置中绑定自定义域名</li>
                        </ol>
                        
                        <h6 class="mt-3">常见问题：</h6>
                        <ul>
                            <li><strong>bad token</strong>：AccessKey 或 SecretKey 错误</li>
                            <li><strong>bucket not exist</strong>：Bucket 名称错误或不存在</li>
                            <li><strong>domain error</strong>：域名格式错误或未绑定</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="alert alert-${isSuccess ? 'success' : 'danger'}">
                    ${message}
                </div>
            `;
        }
        
        function getFormData() {
            const formData = new FormData(document.getElementById('configForm'));
            return formData;
        }
        
        // 验证配置
        document.getElementById('validateBtn').addEventListener('click', function() {
            const formData = getFormData();
            formData.append('action', 'validate_config');
            
            fetch('fix_config.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.valid) {
                    showResult('✓ 配置验证成功！', true);
                } else {
                    showResult('✗ 配置验证失败：<br>' + data.errors.join('<br>'), false);
                }
            })
            .catch(error => {
                showResult('请求失败：' + error.message, false);
            });
        });
        
        // 保存配置
        document.getElementById('saveBtn').addEventListener('click', function() {
            const formData = getFormData();
            formData.append('action', 'update_config');
            
            fetch('fix_config.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showResult(data.success ? '✓ 配置保存成功！' : '✗ 保存失败：' + data.message, data.success);
            })
            .catch(error => {
                showResult('请求失败：' + error.message, false);
            });
        });
        
        // 清空配置
        document.getElementById('clearBtn').addEventListener('click', function() {
            if (confirm('确定要清空所有配置吗？')) {
                const formData = new FormData();
                formData.append('action', 'clear_config');
                
                fetch('fix_config.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    showResult(data.success ? '✓ 配置已清空！' : '✗ 清空失败：' + data.message, data.success);
                    if (data.success) {
                        setTimeout(() => location.reload(), 1000);
                    }
                })
                .catch(error => {
                    showResult('请求失败：' + error.message, false);
                });
            }
        });
    </script>
</body>
</html>
