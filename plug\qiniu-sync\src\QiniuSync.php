<?php
/**
 * 七牛云同步插件主类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 定义插件常量（如果尚未定义）
if (!defined('QINIU_SYNC_PLUGIN')) {
    define('QINIU_SYNC_PLUGIN', true);
}
if (!defined('QINIU_SYNC_PATH')) {
    define('QINIU_SYNC_PATH', dirname(__DIR__));
}

class QiniuSync
{
    private $config;
    private $db;
    private $logger;
    private $configManager;
    private $fileManager;
    private $syncManager;
    private $queueManager;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->loadConfig();
        $this->initializeComponents();
    }
    
    /**
     * 加载配置
     */
    private function loadConfig()
    {
        $configFile = QINIU_SYNC_PATH . '/config/config.php';
        if (!file_exists($configFile)) {
            throw new Exception('配置文件不存在: ' . $configFile);
        }
        
        $this->config = require $configFile;
    }
    
    /**
     * 初始化组件
     */
    private function initializeComponents()
    {
        // 初始化数据库连接
        $this->initializeDatabase();
        
        // 初始化日志记录器
        require_once QINIU_SYNC_PATH . '/src/Logger.php';
        $this->logger = new QiniuSyncLogger($this->config['log']);
        
        // 初始化配置管理器
        require_once QINIU_SYNC_PATH . '/src/ConfigManager.php';
        $this->configManager = new QiniuSyncConfigManager($this->db, $this->config, $this->logger);
        
        // 初始化文件管理器
        require_once QINIU_SYNC_PATH . '/src/FileManager.php';
        $this->fileManager = new QiniuSyncFileManager($this->db, $this->config, $this->logger);
        
        // 初始化同步管理器
        require_once QINIU_SYNC_PATH . '/src/SyncManager.php';
        $this->syncManager = new QiniuSyncSyncManager($this->db, $this->config, $this->logger);
        
        // 初始化队列管理器
        require_once QINIU_SYNC_PATH . '/src/QueueManager.php';
        $this->queueManager = new QiniuSyncQueueManager($this->db, $this->config, $this->logger);
    }
    
    /**
     * 初始化数据库连接
     */
    private function initializeDatabase()
    {
        try {
            // 尝试使用项目的数据库配置
            $dbConfigFile = QINIU_SYNC_PATH . '/../../config.php';
            if (file_exists($dbConfigFile)) {
                include_once $dbConfigFile;

                // 检查数据库配置常量
                if (!defined('DB_HOST') || !defined('DB_NAME') || !defined('DB_USER')) {
                    throw new Exception('数据库配置常量未定义');
                }

                $this->db = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                    DB_USER,
                    DB_PASS,
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
            } else {
                throw new Exception('无法找到数据库配置文件');
            }
        } catch (Exception $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 安装插件
     */
    public function install()
    {
        try {
            $this->logger->info('开始安装七牛云同步插件...');
            
            // 1. 检查依赖
            $this->checkDependencies();
            
            // 2. 创建数据库表
            $this->createDatabaseTables();
            
            // 3. 初始化配置
            $this->initializeConfig();
            
            // 4. 创建必要目录
            $this->createDirectories();
            
            // 5. 设置权限
            $this->setPermissions();
            
            $this->logger->info('七牛云同步插件安装完成');
            return ['success' => true, 'message' => '插件安装成功'];
            
        } catch (Exception $e) {
            $this->logger->error('插件安装失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '插件安装失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 卸载插件
     */
    public function uninstall()
    {
        try {
            $this->logger->info('开始卸载七牛云同步插件...');
            
            // 1. 停止同步服务
            $this->stopSyncService();
            
            // 2. 清理队列
            $this->queueManager->clearQueue();
            
            // 3. 删除数据库表（可选）
            if (isset($_POST['delete_data']) && $_POST['delete_data'] === 'yes') {
                $this->dropDatabaseTables();
            }
            
            // 4. 清理临时文件
            $this->cleanupTempFiles();
            
            $this->logger->info('七牛云同步插件卸载完成');
            return ['success' => true, 'message' => '插件卸载成功'];
            
        } catch (Exception $e) {
            $this->logger->error('插件卸载失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '插件卸载失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 启用插件
     */
    public function enable()
    {
        try {
            $this->configManager->updateConfig('plugin.enabled', true);
            $this->logger->info('七牛云同步插件已启用');
            return ['success' => true, 'message' => '插件启用成功'];
        } catch (Exception $e) {
            $this->logger->error('插件启用失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '插件启用失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 禁用插件
     */
    public function disable()
    {
        try {
            $this->stopSyncService();
            $this->configManager->updateConfig('plugin.enabled', false);
            $this->logger->info('七牛云同步插件已禁用');
            return ['success' => true, 'message' => '插件禁用成功'];
        } catch (Exception $e) {
            $this->logger->error('插件禁用失败: ' . $e->getMessage());
            return ['success' => false, 'message' => '插件禁用失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 获取插件状态
     */
    public function getStatus()
    {
        try {
            $status = [
                'installed' => $this->isInstalled(),
                'enabled' => $this->isEnabled(),
                'sync_running' => $this->isSyncRunning(),
                'queue_size' => $this->queueManager->getQueueSize(),
                'last_sync' => $this->getLastSyncTime(),
                'total_files' => $this->fileManager->getTotalFiles(),
                'sync_stats' => $this->getSyncStats()
            ];
            
            return ['success' => true, 'data' => $status];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 检查依赖
     */
    private function checkDependencies()
    {
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '7.2.0', '<')) {
            throw new Exception('需要PHP 7.2或更高版本');
        }
        
        // 检查必要扩展
        $requiredExtensions = ['curl', 'json', 'mbstring', 'openssl'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                throw new Exception("缺少必要的PHP扩展: {$ext}");
            }
        }
        
        // 检查七牛云SDK
        if (!class_exists('Qiniu\Auth')) {
            throw new Exception('七牛云SDK未安装');
        }
    }
    
    /**
     * 创建数据库表
     */
    private function createDatabaseTables()
    {
        $sqlFile = QINIU_SYNC_PATH . '/sql/install.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('SQL安装文件不存在');
        }
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $this->db->exec($statement);
            }
        }
    }
    
    /**
     * 删除数据库表
     */
    private function dropDatabaseTables()
    {
        $prefix = $this->config['database']['table_prefix'];
        $tables = $this->config['database']['tables'];
        
        foreach ($tables as $table) {
            $tableName = $prefix . $table;
            $this->db->exec("DROP TABLE IF EXISTS `{$tableName}`");
        }
    }
    
    /**
     * 初始化配置
     */
    private function initializeConfig()
    {
        // 生成加密密钥
        $encryptionKey = bin2hex(random_bytes(32));
        $this->configManager->updateConfig('security.encryption_key', $encryptionKey);
        
        // 设置默认配置
        $this->configManager->updateConfig('plugin.enabled', false);
        $this->configManager->updateConfig('sync.enabled', false);
    }
    
    /**
     * 创建必要目录
     */
    private function createDirectories()
    {
        $directories = [
            QINIU_SYNC_PATH . '/logs',
            QINIU_SYNC_PATH . '/cache',
            QINIU_SYNC_PATH . '/temp',
            QINIU_SYNC_PATH . '/backups'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
    
    /**
     * 设置权限
     */
    private function setPermissions()
    {
        $writableDirs = [
            QINIU_SYNC_PATH . '/logs',
            QINIU_SYNC_PATH . '/cache',
            QINIU_SYNC_PATH . '/temp',
            QINIU_SYNC_PATH . '/backups'
        ];
        
        foreach ($writableDirs as $dir) {
            if (is_dir($dir)) {
                chmod($dir, 0755);
            }
        }
    }
    
    /**
     * 停止同步服务
     */
    private function stopSyncService()
    {
        // 实现停止同步服务的逻辑
        $this->syncManager->stop();
    }
    
    /**
     * 清理临时文件
     */
    private function cleanupTempFiles()
    {
        $tempDir = QINIU_SYNC_PATH . '/temp';
        if (is_dir($tempDir)) {
            $files = glob($tempDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * 检查是否已安装
     */
    private function isInstalled()
    {
        try {
            $prefix = $this->config['database']['table_prefix'];
            $stmt = $this->db->query("SHOW TABLES LIKE '{$prefix}config'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 检查是否已启用
     */
    private function isEnabled()
    {
        try {
            return $this->configManager->getConfig('plugin.enabled', false);
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 检查同步是否运行中
     */
    private function isSyncRunning()
    {
        return $this->syncManager->isRunning();
    }
    
    /**
     * 获取最后同步时间
     */
    private function getLastSyncTime()
    {
        return $this->syncManager->getLastSyncTime();
    }
    
    /**
     * 获取同步统计
     */
    private function getSyncStats()
    {
        return $this->syncManager->getStats();
    }
    
    /**
     * 获取配置管理器
     */
    public function getConfigManager()
    {
        return $this->configManager;
    }
    
    /**
     * 获取文件管理器
     */
    public function getFileManager()
    {
        return $this->fileManager;
    }
    
    /**
     * 获取同步管理器
     */
    public function getSyncManager()
    {
        return $this->syncManager;
    }
    
    /**
     * 获取队列管理器
     */
    public function getQueueManager()
    {
        return $this->queueManager;
    }
    
    /**
     * 获取日志记录器
     */
    public function getLogger()
    {
        return $this->logger;
    }
}
