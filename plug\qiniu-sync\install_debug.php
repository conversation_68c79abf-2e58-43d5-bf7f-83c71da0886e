<?php
/**
 * 七牛云同步插件安装程序 - 调试版本
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 引入插件核心文件
require_once __DIR__ . '/bootstrap.php';

// 检查是否为命令行模式
$isCliMode = php_sapi_name() === 'cli';

// 如果是Web模式，检查权限
if (!$isCliMode) {
    session_start();
    // 暂时跳过权限检查用于调试
    // if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    //     header('Location: ../../admin/login.php');
    //     exit;
    // }
}

// 处理AJAX请求
if (!$isCliMode && $_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json; charset=utf-8');
    
    // 添加调试信息
    error_log("收到POST请求: " . file_get_contents('php://input'));
    
    try {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        error_log("解析的数据: " . print_r($data, true));
        
        if (!$data || !isset($data['action'])) {
            throw new Exception('无效的请求');
        }
        
        $action = $data['action'];
        error_log("执行操作: " . $action);
        
        switch ($action) {
            case 'install':
                $result = installPlugin();
                break;
            case 'uninstall':
                $result = uninstallPlugin();
                break;
            case 'enable':
                $result = enablePlugin();
                break;
            case 'disable':
                $result = disablePlugin();
                break;
            case 'test_dependencies':
                $result = testDependencies();
                break;
            case 'get_status':
                $result = getPluginStatus();
                break;
            default:
                $result = ['success' => false, 'message' => '未知操作: ' . $action];
        }
        
        error_log("操作结果: " . print_r($result, true));
        echo json_encode($result, JSON_UNESCAPED_UNICODE);
        exit;
        
    } catch (Exception $e) {
        error_log("异常: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

/**
 * 安装插件
 */
function installPlugin()
{
    try {
        error_log("开始安装插件");
        $qiniuSync = new QiniuSync();
        $result = $qiniuSync->install();
        error_log("安装结果: " . print_r($result, true));
        return $result;
    } catch (Exception $e) {
        error_log("安装异常: " . $e->getMessage());
        return ['success' => false, 'message' => '安装失败: ' . $e->getMessage()];
    }
}

/**
 * 卸载插件
 */
function uninstallPlugin()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->uninstall();
    } catch (Exception $e) {
        return ['success' => false, 'message' => '卸载失败: ' . $e->getMessage()];
    }
}

/**
 * 启用插件
 */
function enablePlugin()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->enable();
    } catch (Exception $e) {
        return ['success' => false, 'message' => '启用失败: ' . $e->getMessage()];
    }
}

/**
 * 禁用插件
 */
function disablePlugin()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->disable();
    } catch (Exception $e) {
        return ['success' => false, 'message' => '禁用失败: ' . $e->getMessage()];
    }
}

/**
 * 测试依赖
 */
function testDependencies()
{
    $results = [];
    
    // 检查PHP版本
    $phpVersion = PHP_VERSION;
    $results['php_version'] = [
        'required' => '7.2.0',
        'current' => $phpVersion,
        'status' => version_compare($phpVersion, '7.2.0', '>=')
    ];
    
    // 检查必要扩展
    $requiredExtensions = ['curl', 'json', 'mbstring', 'openssl', 'pdo', 'pdo_mysql'];
    foreach ($requiredExtensions as $ext) {
        $results['extensions'][$ext] = [
            'required' => true,
            'status' => extension_loaded($ext)
        ];
    }
    
    // 检查七牛云SDK
    $results['qiniu_sdk'] = [
        'required' => true,
        'status' => class_exists('Qiniu\Auth')
    ];
    
    // 检查目录权限
    $directories = [
        QINIU_SYNC_PATH . '/logs',
        QINIU_SYNC_PATH . '/cache',
        QINIU_SYNC_PATH . '/temp'
    ];
    
    foreach ($directories as $dir) {
        $results['directories'][basename($dir)] = [
            'path' => $dir,
            'exists' => is_dir($dir),
            'writable' => is_writable($dir)
        ];
    }
    
    // 检查数据库连接
    try {
        $dbConfigFile = QINIU_SYNC_PATH . '/../../config.php';
        if (file_exists($dbConfigFile)) {
            include_once $dbConfigFile;
            $pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            $results['database'] = [
                'status' => true,
                'message' => '数据库连接正常'
            ];
        } else {
            $results['database'] = [
                'status' => false,
                'message' => '数据库配置文件不存在'
            ];
        }
    } catch (Exception $e) {
        $results['database'] = [
            'status' => false,
            'message' => '数据库连接失败: ' . $e->getMessage()
        ];
    }
    
    // 计算总体状态
    $allPassed = true;
    
    if (!$results['php_version']['status']) {
        $allPassed = false;
    }
    
    foreach ($results['extensions'] as $ext => $info) {
        if (!$info['status']) {
            $allPassed = false;
            break;
        }
    }
    
    if (!$results['qiniu_sdk']['status']) {
        $allPassed = false;
    }
    
    if (!$results['database']['status']) {
        $allPassed = false;
    }
    
    return [
        'success' => $allPassed,
        'message' => $allPassed ? '所有依赖检查通过' : '存在依赖问题',
        'details' => $results
    ];
}

/**
 * 获取插件状态
 */
function getPluginStatus()
{
    try {
        $qiniuSync = new QiniuSync();
        return $qiniuSync->getStatus();
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => '获取状态失败: ' . $e->getMessage(),
            'data' => [
                'installed' => false,
                'enabled' => false,
                'sync_running' => false
            ]
        ];
    }
}

// Web界面
if (!$isCliMode):
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>七牛云同步插件 - 安装程序（调试版）</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cloud-upload"></i> 七牛云同步插件 - 安装程序（调试版）
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- 调试信息 -->
                <div class="alert alert-warning">
                    <h5><i class="bi bi-exclamation-triangle"></i> 调试模式</h5>
                    <p>这是调试版本的安装程序，会显示详细的错误信息。</p>
                    <p>请打开浏览器的开发者工具（F12）查看控制台输出。</p>
                </div>

                <!-- 简化的操作面板 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gear"></i> 调试操作
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-info w-100 mb-2" onclick="testDependencies()">
                                    <i class="bi bi-search"></i> 检查依赖
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-secondary w-100 mb-2" onclick="checkStatus()">
                                    <i class="bi bi-speedometer2"></i> 检查状态
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-primary w-100 mb-2" onclick="installPlugin()">
                                    <i class="bi bi-download"></i> 安装插件
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-success w-100 mb-2" onclick="enablePlugin()">
                                    <i class="bi bi-play"></i> 启用插件
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 调试输出 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-terminal"></i> 调试输出
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-output" class="debug-info">
                            等待操作...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function debugLog(message) {
            console.log('[DEBUG]', message);
            const output = document.getElementById('debug-output');
            output.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            output.scrollTop = output.scrollHeight;
        }

        function sendRequest(action, data = {}) {
            debugLog('发送请求: ' + action);
            
            const requestData = { action: action, ...data };
            debugLog('请求数据: ' + JSON.stringify(requestData));
            
            return fetch('install_debug.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                debugLog('响应状态: ' + response.status);
                return response.text();
            })
            .then(text => {
                debugLog('响应内容: ' + text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    debugLog('JSON解析失败: ' + e.message);
                    throw new Error('服务器返回了无效的JSON: ' + text);
                }
            })
            .then(result => {
                debugLog('解析结果: ' + JSON.stringify(result));
                return result;
            })
            .catch(error => {
                debugLog('请求错误: ' + error.message);
                throw error;
            });
        }

        function testDependencies() {
            debugLog('开始检查依赖...');
            sendRequest('test_dependencies')
                .then(result => {
                    if (result.success) {
                        debugLog('✅ 依赖检查通过');
                    } else {
                        debugLog('❌ 依赖检查失败: ' + result.message);
                    }
                })
                .catch(error => {
                    debugLog('❌ 依赖检查出错: ' + error.message);
                });
        }

        function checkStatus() {
            debugLog('开始检查状态...');
            sendRequest('get_status')
                .then(result => {
                    if (result.success) {
                        debugLog('✅ 状态获取成功');
                        debugLog('状态信息: ' + JSON.stringify(result.data));
                    } else {
                        debugLog('❌ 状态获取失败: ' + result.message);
                    }
                })
                .catch(error => {
                    debugLog('❌ 状态检查出错: ' + error.message);
                });
        }

        function installPlugin() {
            debugLog('开始安装插件...');
            sendRequest('install')
                .then(result => {
                    if (result.success) {
                        debugLog('✅ 插件安装成功');
                    } else {
                        debugLog('❌ 插件安装失败: ' + result.message);
                    }
                })
                .catch(error => {
                    debugLog('❌ 插件安装出错: ' + error.message);
                });
        }

        function enablePlugin() {
            debugLog('开始启用插件...');
            sendRequest('enable')
                .then(result => {
                    if (result.success) {
                        debugLog('✅ 插件启用成功');
                    } else {
                        debugLog('❌ 插件启用失败: ' + result.message);
                    }
                })
                .catch(error => {
                    debugLog('❌ 插件启用出错: ' + error.message);
                });
        }

        // 页面加载完成后自动检查状态
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('页面加载完成，开始初始化...');
            checkStatus();
        });
    </script>
</body>
</html>
<?php endif; ?>
